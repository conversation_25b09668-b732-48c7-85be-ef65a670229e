/**
 * 简单拖拽测试组件
 * 用于快速验证地图拖拽功能
 */

import { _decorator, Component, Node, Vec3, input, Input, EventTouch, UITransform } from 'cc';

const { ccclass, property } = _decorator;

@ccclass('SimpleDragTest')
export class SimpleDragTest extends Component {
    
    @property({ tooltip: '是否启用拖拽' })
    public enableDrag: boolean = true;
    
    @property({ tooltip: '拖拽边界左' })
    public boundLeft: number = -400;
    
    @property({ tooltip: '拖拽边界右' })
    public boundRight: number = 400;
    
    @property({ tooltip: '拖拽边界上' })
    public boundTop: number = 300;
    
    @property({ tooltip: '拖拽边界下' })
    public boundBottom: number = -300;
    
    // 私有属性
    private _isDragging: boolean = false;
    private _lastTouchPosition: Vec3 = new Vec3();
    private _startPosition: Vec3 = new Vec3();

    protected onLoad(): void {
        console.log('🎮 SimpleDragTest: 简单拖拽测试组件加载');
        this.setupInputEvents();
    }

    protected onDestroy(): void {
        this.cleanupInputEvents();
    }

    /**
     * 设置输入事件
     */
    private setupInputEvents(): void {
        if (!this.enableDrag) return;
        
        // 触摸事件
        this.node.on(Input.EventType.TOUCH_START, this.onTouchStart, this);
        this.node.on(Input.EventType.TOUCH_MOVE, this.onTouchMove, this);
        this.node.on(Input.EventType.TOUCH_END, this.onTouchEnd, this);
        this.node.on(Input.EventType.TOUCH_CANCEL, this.onTouchEnd, this);
        
        console.log('🎮 SimpleDragTest: 输入事件设置完成');
    }

    /**
     * 清理输入事件
     */
    private cleanupInputEvents(): void {
        this.node.off(Input.EventType.TOUCH_START, this.onTouchStart, this);
        this.node.off(Input.EventType.TOUCH_MOVE, this.onTouchMove, this);
        this.node.off(Input.EventType.TOUCH_END, this.onTouchEnd, this);
        this.node.off(Input.EventType.TOUCH_CANCEL, this.onTouchEnd, this);
    }

    /**
     * 触摸开始
     */
    private onTouchStart(event: EventTouch): void {
        if (!this.enableDrag) return;
        
        this._isDragging = true;
        this._lastTouchPosition.set(event.getLocation().x, event.getLocation().y, 0);
        this._startPosition.set(this.node.position);
        
        console.log('🎮 SimpleDragTest: 开始拖拽', this._lastTouchPosition);
    }

    /**
     * 触摸移动
     */
    private onTouchMove(event: EventTouch): void {
        if (!this.enableDrag || !this._isDragging) return;
        
        const currentPosition = event.getLocation();
        const deltaX = currentPosition.x - this._lastTouchPosition.x;
        const deltaY = currentPosition.y - this._lastTouchPosition.y;
        
        // 计算新位置
        const newPosition = new Vec3(
            this.node.position.x + deltaX,
            this.node.position.y + deltaY,
            this.node.position.z
        );
        
        // 应用边界限制
        this.applyBounds(newPosition);
        
        // 设置新位置
        this.node.setPosition(newPosition);
        
        // 更新上次触摸位置
        this._lastTouchPosition.set(currentPosition.x, currentPosition.y, 0);
    }

    /**
     * 触摸结束
     */
    private onTouchEnd(event: EventTouch): void {
        if (!this.enableDrag) return;
        
        this._isDragging = false;
        console.log('🎮 SimpleDragTest: 结束拖拽', this.node.position);
    }

    /**
     * 应用边界限制
     */
    private applyBounds(position: Vec3): void {
        position.x = Math.max(this.boundLeft, Math.min(this.boundRight, position.x));
        position.y = Math.max(this.boundBottom, Math.min(this.boundTop, position.y));
    }

    /**
     * 重置位置
     */
    public resetPosition(): void {
        this.node.setPosition(Vec3.ZERO);
        console.log('🎮 SimpleDragTest: 重置位置');
    }

    /**
     * 设置边界
     */
    public setBounds(left: number, right: number, top: number, bottom: number): void {
        this.boundLeft = left;
        this.boundRight = right;
        this.boundTop = top;
        this.boundBottom = bottom;
        
        console.log('🎮 SimpleDragTest: 设置边界', { left, right, top, bottom });
    }

    /**
     * 移动到指定位置
     */
    public moveTo(position: Vec3): void {
        const newPosition = new Vec3(position);
        this.applyBounds(newPosition);
        this.node.setPosition(newPosition);
        
        console.log('🎮 SimpleDragTest: 移动到位置', newPosition);
    }
}
