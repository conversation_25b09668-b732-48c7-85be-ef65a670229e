/**
 * 可屏蔽输入的按钮组件
 * 在地图拖拽时自动屏蔽按钮点击
 */

import { _decorator, Component, Button, EventHandler } from 'cc';
import { DraggableMapContainer } from './DraggableMapContainer';

const { ccclass, property } = _decorator;

@ccclass('InputBlockableButton')
export class InputBlockableButton extends Component {
    
    @property({ tooltip: '是否启用输入屏蔽检查' })
    public enableInputBlocking: boolean = true;
    
    @property({ tooltip: '是否显示屏蔽日志' })
    public showBlockingLog: boolean = false;
    
    // 私有属性
    private _button: Button | null = null;
    private _originalClickEvents: EventHandler[] = [];

    protected onLoad(): void {
        this.initializeButton();
    }

    protected onDestroy(): void {
        this.restoreOriginalEvents();
    }

    /**
     * 初始化按钮
     */
    private initializeButton(): void {
        this._button = this.node.getComponent(Button);
        if (!this._button) {
            console.warn('🚫 InputBlockableButton: 节点上没有Button组件', this.node.name);
            return;
        }
        
        // 备份原始点击事件
        this._originalClickEvents = [...this._button.clickEvents];
        
        // 替换为带屏蔽检查的事件处理
        this.setupBlockableEvents();
        
        console.log('🚫 InputBlockableButton: 按钮输入屏蔽初始化完成', this.node.name);
    }

    /**
     * 设置可屏蔽的事件处理
     */
    private setupBlockableEvents(): void {
        if (!this._button) return;
        
        // 清空现有事件
        this._button.clickEvents = [];
        
        // 为每个原始事件创建包装的事件处理器
        this._originalClickEvents.forEach((originalEvent, index) => {
            const wrappedEvent = new EventHandler();
            wrappedEvent.target = this.node;
            wrappedEvent.component = 'InputBlockableButton';
            wrappedEvent.handler = 'onWrappedClick';
            wrappedEvent.customEventData = index.toString(); // 传递原始事件索引
            
            this._button!.clickEvents.push(wrappedEvent);
        });
    }

    /**
     * 包装的点击事件处理
     */
    public onWrappedClick(event: any, customEventData: string): void {
        // 检查输入是否被屏蔽
        if (this.enableInputBlocking && DraggableMapContainer.isInputBlocked()) {
            if (this.showBlockingLog) {
                console.log('🚫 InputBlockableButton: 输入被屏蔽，忽略点击', this.node.name);
            }
            return;
        }
        
        // 获取原始事件索引
        const eventIndex = parseInt(customEventData);
        if (eventIndex >= 0 && eventIndex < this._originalClickEvents.length) {
            const originalEvent = this._originalClickEvents[eventIndex];
            
            // 调用原始事件处理器
            this.invokeOriginalEvent(originalEvent, event);
        }
    }

    /**
     * 调用原始事件处理器
     */
    private invokeOriginalEvent(eventHandler: EventHandler, event: any): void {
        if (!eventHandler.target || !eventHandler.component || !eventHandler.handler) {
            return;
        }
        
        try {
            const targetComponent = eventHandler.target.getComponent(eventHandler.component);
            if (targetComponent && typeof targetComponent[eventHandler.handler] === 'function') {
                // 调用原始处理方法
                targetComponent[eventHandler.handler](event, eventHandler.customEventData);
            }
        } catch (error) {
            console.error('🚫 InputBlockableButton: 调用原始事件处理器失败', error);
        }
    }

    /**
     * 恢复原始事件
     */
    private restoreOriginalEvents(): void {
        if (this._button && this._originalClickEvents.length > 0) {
            this._button.clickEvents = [...this._originalClickEvents];
        }
    }

    // ==================== 公共API ====================

    /**
     * 启用/禁用输入屏蔽检查
     */
    public setInputBlockingEnabled(enabled: boolean): void {
        this.enableInputBlocking = enabled;
        console.log('🚫 InputBlockableButton: 输入屏蔽检查', enabled ? '启用' : '禁用', this.node.name);
    }

    /**
     * 启用/禁用屏蔽日志
     */
    public setBlockingLogEnabled(enabled: boolean): void {
        this.showBlockingLog = enabled;
    }

    /**
     * 手动触发点击（忽略屏蔽）
     */
    public forceClick(): void {
        if (!this._button) return;
        
        console.log('🚫 InputBlockableButton: 强制触发点击', this.node.name);
        
        // 直接调用所有原始事件
        this._originalClickEvents.forEach(eventHandler => {
            this.invokeOriginalEvent(eventHandler, null);
        });
    }

    /**
     * 检查当前是否被屏蔽
     */
    public isCurrentlyBlocked(): boolean {
        return this.enableInputBlocking && DraggableMapContainer.isInputBlocked();
    }

    /**
     * 获取按钮组件
     */
    public getButton(): Button | null {
        return this._button;
    }

    /**
     * 重新初始化（用于动态添加事件后）
     */
    public reinitialize(): void {
        this.restoreOriginalEvents();
        this.initializeButton();
        console.log('🚫 InputBlockableButton: 重新初始化完成', this.node.name);
    }

    // ==================== 静态工具方法 ====================

    /**
     * 为节点添加输入屏蔽功能
     */
    public static addToNode(node: any): InputBlockableButton | null {
        if (!node) return null;
        
        let blockableButton = node.getComponent(InputBlockableButton);
        if (!blockableButton) {
            blockableButton = node.addComponent(InputBlockableButton);
        }
        
        return blockableButton;
    }

    /**
     * 批量为节点数组添加输入屏蔽功能
     */
    public static addToNodes(nodes: any[]): InputBlockableButton[] {
        const results: InputBlockableButton[] = [];
        
        nodes.forEach(node => {
            const blockableButton = InputBlockableButton.addToNode(node);
            if (blockableButton) {
                results.push(blockableButton);
            }
        });
        
        console.log('🚫 InputBlockableButton: 批量添加输入屏蔽完成', results.length);
        return results;
    }

    /**
     * 全局启用/禁用所有按钮的输入屏蔽
     */
    public static setGlobalInputBlocking(enabled: boolean): void {
        // 这里可以添加全局控制逻辑
        console.log('🚫 InputBlockableButton: 全局输入屏蔽', enabled ? '启用' : '禁用');
    }
}
