# 线性分支地图系统实现方案

## 概述

这个系统实现了原型图中的中间交互区域，提供了一个可拖拽的线性分支地图，玩家可以通过拖动地图找到目标行为按钮并进行交互。

## 核心组件

### 1. LinearBranchMapPanel (线性分支地图面板)
- **功能**: 管理可拖拽的场景地图和线性分支的交互节点
- **特点**: 
  - 支持背景图片显示
  - 自动生成线性分支节点
  - 节点间的递进解锁关系
  - 可拖拽和缩放的地图视图

### 2. InteractiveMapController (交互地图控制器)
- **功能**: 整合地图面板和行为面板，提供完整的交互逻辑
- **特点**:
  - 自动处理节点点击事件
  - 管理行为执行流程
  - 处理节点解锁逻辑
  - 提供统一的事件接口

### 3. LinearBranchMapScene (线性分支地图场景)
- **功能**: 演示场景脚本，展示如何使用整个系统
- **特点**:
  - 自动创建UI结构
  - 键盘快捷键支持
  - 调试模式
  - 完整的事件处理

## 系统架构

```
LinearBranchMapScene (场景控制)
    ├── InteractiveMapController (控制器)
    │   ├── LinearBranchMapPanel (地图面板)
    │   │   ├── DraggableMapContainer (拖拽容器)
    │   │   └── InteractionNode (交互节点)
    │   └── BehaviorPanel (行为面板)
    └── EventManager (事件管理)
```

## 使用方法

### 1. 在场景中设置

```typescript
// 在场景节点上添加LinearBranchMapScene组件
const sceneController = this.node.addComponent(LinearBranchMapScene);

// 配置参数
sceneController.autoCreateUI = true;
sceneController.debugMode = true;
```

### 2. 手动创建UI结构

```typescript
// 创建地图面板
const mapPanelNode = new Node('LinearBranchMapPanel');
const mapPanel = mapPanelNode.addComponent(LinearBranchMapPanel);

// 配置地图参数
mapPanel.backgroundImagePath = 'ui/scene/BattleScene1';
mapPanel.nodeSpacingX = 200;
mapPanel.nodeSpacingY = 150;
mapPanel.branchCount = 3;
mapPanel.nodesPerBranch = 5;

// 创建行为面板
const behaviorPanelNode = new Node('BehaviorPanel');
const behaviorPanel = behaviorPanelNode.addComponent(BehaviorPanel);

// 创建控制器
const controllerNode = new Node('InteractiveMapController');
const controller = controllerNode.addComponent(InteractiveMapController);
controller.branchMapPanel = mapPanel;
controller.behaviorPanel = behaviorPanel;
```

### 3. 监听事件

```typescript
const eventManager = EventManager.getInstance();

// 监听节点点击
eventManager.on('branch_node_clicked', (eventData) => {
    const { nodeData, behaviorType } = eventData;
    console.log('节点被点击:', nodeData.name);
});

// 监听行为完成
eventManager.on('map_behavior_completed', (eventData) => {
    const { behaviorData, nodeData } = eventData;
    console.log('行为完成:', behaviorData?.name);
});

// 监听节点解锁
eventManager.on('branch_node_unlocked', (eventData) => {
    const { nodeId, nodeData } = eventData;
    console.log('节点解锁:', nodeData.name);
});
```

## 配置参数

### LinearBranchMapPanel 配置

| 参数 | 类型 | 默认值 | 说明 |
|------|------|--------|------|
| backgroundImagePath | string | 'ui/scene/BattleScene1' | 背景图片路径 |
| nodeSpacingX | number | 200 | 节点水平间距 |
| nodeSpacingY | number | 150 | 节点垂直间距 |
| branchCount | number | 3 | 分支数量 |
| nodesPerBranch | number | 5 | 每分支节点数 |

### InteractiveMapController 配置

| 参数 | 类型 | 默认值 | 说明 |
|------|------|--------|------|
| autoShowMap | boolean | true | 是否自动显示地图 |
| autoHandleBehavior | boolean | true | 是否自动处理行为 |
| defaultBehaviorDuration | number | 3.0 | 默认行为持续时间 |

## 节点数据结构

```typescript
interface IBranchNodeData {
    id: string;                    // 节点ID
    name: string;                  // 节点名称
    description: string;           // 节点描述
    behaviorType: BehaviorType;    // 行为类型
    position: Vec3;                // 世界坐标位置
    unlocked: boolean;             // 是否已解锁
    prerequisites: string[];       // 前置节点ID列表
    unlocks: string[];             // 解锁的后续节点ID列表
    level: number;                 // 节点等级
    behaviorData?: IBehaviorData;  // 行为数据
    iconPath?: string;             // 图标路径
    customData?: any;              // 自定义数据
}
```

## 事件系统

### 发送的事件

- `branch_node_clicked` - 分支节点被点击
- `trigger_behavior` - 触发行为
- `branch_node_unlocked` - 分支节点解锁
- `map_node_activated` - 地图节点激活
- `map_behavior_completed` - 地图行为完成
- `show_ui_message` - 显示UI消息

### 监听的事件

- `behavior_complete` - 行为完成
- `behavior_start` - 行为开始
- `behavior_stop` - 行为停止

## 键盘快捷键

- `1` - 聚焦到起点
- `2` - 聚焦到第一分支
- `3` - 聚焦到第二分支
- `R` - 重置地图位置
- `+` - 放大地图
- `-` - 缩小地图
- `S` - 停止当前行为
- `H` - 显示帮助信息
- `ESC` - 返回主场景

## 扩展功能

### 1. 自定义节点类型

```typescript
// 添加新的行为类型
enum CustomBehaviorType {
    Craft = 'craft',
    Trade = 'trade',
    Explore = 'explore'
}

// 注册自定义行为
behaviorPanel.registerBehavior({
    type: CustomBehaviorType.Craft,
    name: '制作',
    description: '制作物品',
    duration: 5.0,
    interruptible: true
});
```

### 2. 自定义节点布局

```typescript
// 重写节点生成逻辑
class CustomLinearBranchMapPanel extends LinearBranchMapPanel {
    protected generateBranchNodeData(): void {
        // 自定义节点布局逻辑
    }
}
```

### 3. 添加特效和动画

```typescript
// 监听解锁事件添加特效
eventManager.on('play_unlock_effect', (eventData) => {
    const { nodeData, position } = eventData;
    // 播放粒子特效
    // 播放音效
    // 显示动画
});
```

## 注意事项

1. **资源路径**: 确保背景图片资源路径正确
2. **事件管理**: 及时清理事件监听器避免内存泄漏
3. **性能优化**: 大量节点时考虑对象池和视口裁剪
4. **兼容性**: 确保与现有的BehaviorPanel系统兼容

## 调试技巧

1. 启用调试模式查看详细日志
2. 使用键盘快捷键快速测试功能
3. 监听事件输出了解系统状态
4. 检查节点数据结构确保正确性

这个系统提供了完整的线性分支交互地图解决方案，可以根据具体需求进行定制和扩展。
