# 线性分支地图系统故障排除指南

## 常见问题及解决方案

### 1. 编译错误

#### 问题: "Camera is not defined"
**原因**: 缺少Camera组件的导入
**解决方案**: 
```typescript
// 在文件顶部添加Camera导入
import { _decorator, Component, Node, Camera } from 'cc';
```

#### 问题: "DraggableMapContainer is not defined"
**原因**: 缺少DraggableMapContainer组件的导入
**解决方案**:
```typescript
import { DraggableMapContainer } from '../components/DraggableMapContainer';
```

#### 问题: "InteractionNode is not defined"
**原因**: 缺少InteractionNode组件的导入
**解决方案**:
```typescript
import { InteractionNode } from '../components/InteractionNode';
```

### 2. 运行时错误

#### 问题: 地图面板不显示
**可能原因**:
1. 背景图片路径错误
2. 节点层级结构不正确
3. UITransform组件缺失

**解决方案**:
```typescript
// 检查背景图片路径
mapPanel.backgroundImagePath = 'ui/scene/BattleScene1'; // 确保路径正确

// 检查节点结构
console.log('地图容器:', mapPanel.mapContainer);
console.log('背景节点:', mapPanel.backgroundNode);
```

#### 问题: 节点点击无响应
**可能原因**:
1. 事件监听器未正确绑定
2. 节点被其他UI遮挡
3. 触摸区域设置错误

**解决方案**:
```typescript
// 检查事件绑定
EventManager.getInstance().on('branch_node_clicked', this.onNodeClicked, this);

// 检查节点层级
node.setSiblingIndex(999); // 确保节点在最上层
```

#### 问题: 拖拽功能不工作
**可能原因**:
1. DraggableMapContainer组件未添加
2. 输入事件被其他组件拦截
3. 拖拽边界设置错误

**解决方案**:
```typescript
// 确保拖拽组件存在
const draggable = mapContainer.getComponent(DraggableMapContainer);
if (!draggable) {
    mapContainer.addComponent(DraggableMapContainer);
}

// 设置正确的拖拽边界
draggable.setBounds(-800, 800, 400, -400);
```

### 3. 性能问题

#### 问题: 大量节点时卡顿
**解决方案**:
1. 启用对象池
2. 实现视口裁剪
3. 减少节点数量或分批加载

```typescript
// 视口裁剪示例
private updateNodeVisibility(): void {
    const viewBounds = this.getViewBounds();
    this._branchNodes.forEach((node, id) => {
        const nodePos = node.node.position;
        const visible = this.isInViewBounds(nodePos, viewBounds);
        node.node.active = visible;
    });
}
```

#### 问题: 内存泄漏
**解决方案**:
1. 及时清理事件监听器
2. 销毁不需要的节点
3. 清理定时器和动画

```typescript
protected onDestroy(): void {
    // 清理事件监听器
    EventManager.getInstance().off('branch_node_clicked', this.onNodeClicked, this);
    
    // 清理节点引用
    this._branchNodes.clear();
    this._nodeData.clear();
}
```

### 4. 集成问题

#### 问题: 与现有UI系统冲突
**解决方案**:
1. 检查UI层级
2. 确保事件不冲突
3. 使用正确的面板管理

```typescript
// 正确的面板显示方式
public showBranchMap(): void {
    // 先隐藏其他面板
    this.hideOtherPanels();
    
    // 再显示地图面板
    if (this.branchMapPanel) {
        this.branchMapPanel.showPanel();
    }
}
```

#### 问题: 行为系统不响应
**解决方案**:
1. 检查BehaviorPanel是否正确引用
2. 确保事件名称匹配
3. 验证行为数据格式

```typescript
// 检查行为面板引用
if (!this.behaviorPanel) {
    this.behaviorPanel = find('Canvas/MainUI/BehaviorPanel')?.getComponent(BehaviorPanel);
}

// 确保事件数据格式正确
EventManager.getInstance().emit('trigger_behavior', {
    behaviorType: BehaviorType.Attack,
    nodeData: nodeData
});
```

### 5. 调试技巧

#### 启用调试模式
```typescript
// 在组件中启用调试
this.debugMode = true;

// 查看详细日志
console.log('🗺️ 地图状态:', {
    nodeCount: this._branchNodes.size,
    activeNodes: Array.from(this._branchNodes.keys()),
    mapPosition: this.mapContainer?.position
});
```

#### 使用测试脚本
```typescript
// 添加测试组件
const test = this.node.addComponent(LinearBranchMapTest);
test.runAllTests();
```

#### 检查组件状态
```typescript
// 检查关键组件是否存在
const checkComponents = () => {
    console.log('组件检查:', {
        mapPanel: !!this.branchMapPanel,
        mapController: !!this.mapController,
        behaviorPanel: !!this.behaviorPanel,
        eventManager: !!EventManager.getInstance()
    });
};
```

### 6. 快速修复清单

在遇到问题时，按以下顺序检查：

1. **编译错误**
   - [ ] 检查所有import语句
   - [ ] 确保组件路径正确
   - [ ] 验证TypeScript语法

2. **运行时错误**
   - [ ] 检查控制台错误信息
   - [ ] 验证节点层级结构
   - [ ] 确认资源路径正确

3. **功能异常**
   - [ ] 检查事件绑定
   - [ ] 验证组件引用
   - [ ] 确认配置参数

4. **性能问题**
   - [ ] 检查节点数量
   - [ ] 监控内存使用
   - [ ] 优化渲染性能

### 7. 联系支持

如果以上方法都无法解决问题，请提供以下信息：

1. 错误信息的完整堆栈跟踪
2. 相关代码片段
3. Cocos Creator版本
4. 操作系统信息
5. 重现步骤

### 8. 有用的调试命令

```typescript
// 在浏览器控制台中运行这些命令进行调试

// 查看地图状态
window.debugMapState = () => {
    const mainUI = find('Canvas/MainUI')?.getComponent(MainUIController);
    const mapPanel = mainUI?.getBranchMapPanel();
    console.log('地图状态:', mapPanel);
};

// 强制显示地图
window.showMap = () => {
    const mainUI = find('Canvas/MainUI')?.getComponent(MainUIController);
    mainUI?.showBranchMap();
};

// 重置地图
window.resetMap = () => {
    const mainUI = find('Canvas/MainUI')?.getComponent(MainUIController);
    const controller = mainUI?.getMapController();
    controller?.resetMapState();
};
```

这个故障排除指南应该能帮助你解决大部分常见问题。如果遇到特殊情况，可以根据具体错误信息进行针对性的修复。
