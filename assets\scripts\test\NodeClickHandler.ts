/**
 * 节点点击处理器
 * 处理地图节点的点击事件并触发行为系统
 */

import { _decorator, Component, Node, find } from 'cc';
import { BehaviorPanel, BehaviorType } from '../ui/panels/BehaviorPanel';

const { ccclass, property } = _decorator;

@ccclass('NodeClickHandler')
export class NodeClickHandler extends Component {
    
    @property({ tooltip: '节点ID' })
    public nodeId: string = '';
    
    @property({ tooltip: '节点名称' })
    public nodeName: string = '';
    
    @property({ tooltip: '行为类型' })
    public behaviorType: BehaviorType = BehaviorType.Explore;
    
    @property({ tooltip: '是否已解锁' })
    public unlocked: boolean = false;
    
    @property({ tooltip: '节点等级' })
    public level: number = 1;
    
    // 私有属性
    private _behaviorPanel: BehaviorPanel | null = null;

    protected onLoad(): void {
        console.log('🎯 NodeClickHandler: 节点点击处理器加载', this.nodeName);
        this.initializeComponents();
        this.setupNodeData();
    }

    /**
     * 初始化组件
     */
    private initializeComponents(): void {
        // 查找行为面板
        this._behaviorPanel = find('Canvas/MainUI/BehaviorPanel')?.getComponent(BehaviorPanel);
        
        if (!this._behaviorPanel) {
            console.warn('🎯 NodeClickHandler: 未找到行为面板');
        }
        
        console.log('🎯 NodeClickHandler: 组件初始化完成', {
            behaviorPanel: !!this._behaviorPanel
        });
    }

    /**
     * 设置节点数据
     */
    private setupNodeData(): void {
        // 如果没有设置nodeId，使用节点名称
        if (!this.nodeId) {
            this.nodeId = this.node.name.toLowerCase();
        }
        
        // 如果没有设置nodeName，使用节点名称
        if (!this.nodeName) {
            this.nodeName = this.node.name;
        }
        
        // 根据节点名称自动设置行为类型
        this.behaviorType = this.getBehaviorTypeFromName(this.node.name);
        
        // 起始节点默认解锁
        if (this.node.name.includes('Start')) {
            this.unlocked = true;
        }
        
        console.log('🎯 NodeClickHandler: 节点数据设置完成', {
            nodeId: this.nodeId,
            nodeName: this.nodeName,
            behaviorType: this.behaviorType,
            unlocked: this.unlocked,
            level: this.level
        });
    }

    /**
     * 根据节点名称获取行为类型
     */
    private getBehaviorTypeFromName(nodeName: string): BehaviorType {
        if (nodeName.includes('Start')) {
            return BehaviorType.Explore;
        } else if (nodeName.includes('Branch1')) {
            return BehaviorType.Battle;
        } else if (nodeName.includes('Branch2')) {
            return BehaviorType.Gather;
        } else if (nodeName.includes('Branch3')) {
            return BehaviorType.Study;
        }
        return BehaviorType.Explore;
    }

    /**
     * 处理节点点击
     */
    public onNodeClick(): void {
        console.log('🎯 NodeClickHandler: 节点被点击', this.nodeName);
        
        // 检查是否已解锁
        if (!this.unlocked) {
            console.log('🔒 节点未解锁:', this.nodeName);
            this.showLockedMessage();
            return;
        }
        
        // 触发行为
        this.triggerBehavior();
    }

    /**
     * 显示锁定消息
     */
    private showLockedMessage(): void {
        console.log('🔒 ' + this.nodeName + ' 尚未解锁，请先完成前置任务');
    }

    /**
     * 触发行为
     */
    private triggerBehavior(): void {
        if (!this._behaviorPanel) {
            console.warn('🎯 NodeClickHandler: 行为面板未找到，无法触发行为');
            return;
        }
        
        console.log('🎯 NodeClickHandler: 触发行为', {
            node: this.nodeName,
            type: this.behaviorType
        });
        
        // 创建行为数据
        const behaviorData = {
            name: `${this.nodeName}任务`,
            description: `执行${this.nodeName}的相关行为`,
            type: this.behaviorType,
            duration: 3.0 + (this.level * 0.5), // 根据等级调整持续时间
            rewards: {
                exp: 100 * this.level,
                gold: 50 * this.level
            }
        };
        
        // 执行行为
        this._behaviorPanel.executeBehavior(behaviorData);
        
        // 监听行为完成
        this.scheduleOnce(() => {
            this.onBehaviorCompleted();
        }, behaviorData.duration + 0.5);
    }

    /**
     * 行为完成处理
     */
    private onBehaviorCompleted(): void {
        console.log('🎯 NodeClickHandler: 行为完成', this.nodeName);
        
        // 显示奖励
        this.showRewards();
        
        // 尝试解锁下一个节点
        this.tryUnlockNextNode();
    }

    /**
     * 显示奖励
     */
    private showRewards(): void {
        const rewards = {
            经验值: 100 * this.level,
            金币: 50 * this.level
        };
        
        console.log('🎁 完成任务获得奖励:', rewards);
    }

    /**
     * 尝试解锁下一个节点
     */
    private tryUnlockNextNode(): void {
        // 查找地图容器中的所有节点
        const mapContainer = find('Canvas/MainUI/BackPanel/LinearBranchMapContainer/MapContainer');
        if (!mapContainer) return;
        
        const allNodes = mapContainer.children;
        const currentIndex = allNodes.indexOf(this.node);
        
        // 解锁下一个节点
        if (currentIndex >= 0 && currentIndex < allNodes.length - 1) {
            const nextNode = allNodes[currentIndex + 1];
            const nextHandler = nextNode.getComponent(NodeClickHandler);
            
            if (nextHandler && !nextHandler.unlocked) {
                nextHandler.unlockNode();
                console.log('✨ 解锁新节点:', nextHandler.nodeName);
            }
        }
    }

    // ==================== 公共API ====================

    /**
     * 解锁节点
     */
    public unlockNode(): void {
        this.unlocked = true;
        this.updateVisualState();
        console.log('🔓 节点已解锁:', this.nodeName);
    }

    /**
     * 锁定节点
     */
    public lockNode(): void {
        this.unlocked = false;
        this.updateVisualState();
        console.log('🔒 节点已锁定:', this.nodeName);
    }

    /**
     * 更新视觉状态
     */
    private updateVisualState(): void {
        const sprite = this.node.getComponent('cc.Sprite');
        if (sprite) {
            // 解锁状态下恢复原色，锁定状态下变暗
            const alpha = this.unlocked ? 255 : 128;
            sprite.color = sprite.color.clone();
            sprite.color.a = alpha;
        }
    }

    /**
     * 获取节点数据
     */
    public getNodeData(): any {
        return {
            id: this.nodeId,
            name: this.nodeName,
            behaviorType: this.behaviorType,
            unlocked: this.unlocked,
            level: this.level
        };
    }

    /**
     * 设置节点数据
     */
    public setNodeData(data: any): void {
        this.nodeId = data.id || this.nodeId;
        this.nodeName = data.name || this.nodeName;
        this.behaviorType = data.behaviorType || this.behaviorType;
        this.unlocked = data.unlocked !== undefined ? data.unlocked : this.unlocked;
        this.level = data.level || this.level;
        
        this.updateVisualState();
    }
}
