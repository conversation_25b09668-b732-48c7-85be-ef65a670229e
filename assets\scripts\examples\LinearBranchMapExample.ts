/**
 * 线性分支地图使用示例
 * 展示如何在现有场景中集成和使用线性分支地图系统
 */

import { _decorator, Component, Node, Button, find, input, Input, EventKeyboard, KeyCode } from 'cc';
import { EventManager } from '../managers/EventManager';
import { LinearBranchMapPanel } from '../ui/panels/LinearBranchMapPanel';
import { InteractiveMapController } from '../ui/controllers/InteractiveMapController';
import { BehaviorPanel } from '../ui/panels/BehaviorPanel';
import { MainUIController } from '../ui/MainUIController';

const { ccclass, property } = _decorator;

@ccclass('LinearBranchMapExample')
export class LinearBranchMapExample extends Component {
    
    @property({ type: Button, tooltip: '显示地图按钮' })
    public showMapButton: Button | null = null;
    
    @property({ type: Button, tooltip: '隐藏地图按钮' })
    public hideMapButton: Button | null = null;
    
    @property({ type: Button, tooltip: '重置地图按钮' })
    public resetMapButton: Button | null = null;
    
    @property({ type: MainUIController, tooltip: '主UI控制器' })
    public mainUIController: MainUIController | null = null;
    
    @property({ tooltip: '是否启用键盘快捷键' })
    public enableKeyboardShortcuts: boolean = true;
    
    @property({ tooltip: '是否自动显示地图' })
    public autoShowMap: boolean = true;
    
    // 私有属性
    private _mapController: InteractiveMapController | null = null;
    private _branchMapPanel: LinearBranchMapPanel | null = null;
    private _behaviorPanel: BehaviorPanel | null = null;

    protected onLoad(): void {
        console.log('📖 LinearBranchMapExample: 线性分支地图示例加载');
        this.initializeComponents();
        this.setupEventListeners();
        
        if (this.enableKeyboardShortcuts) {
            this.setupKeyboardShortcuts();
        }
    }

    protected start(): void {
        this.setupButtons();
        
        if (this.autoShowMap) {
            this.scheduleOnce(() => {
                this.showMap();
            }, 1.0); // 延迟1秒显示，确保所有组件都已初始化
        }
        
        this.showInstructions();
    }

    protected onDestroy(): void {
        this.cleanupEventListeners();
        
        if (this.enableKeyboardShortcuts) {
            input.off(Input.EventType.KEY_DOWN, this.onKeyDown, this);
        }
    }

    /**
     * 初始化组件
     */
    private initializeComponents(): void {
        // 自动查找主UI控制器
        if (!this.mainUIController) {
            this.mainUIController = find('Canvas/MainUI')?.getComponent(MainUIController) ||
                                   this.node.getComponentInParent(MainUIController);
        }
        
        // 从主UI控制器获取地图相关组件
        if (this.mainUIController) {
            this._mapController = this.mainUIController.getMapController();
            this._branchMapPanel = this.mainUIController.getBranchMapPanel();
        }
        
        // 查找行为面板
        this._behaviorPanel = find('Canvas/MainUI/BehaviorPanel')?.getComponent(BehaviorPanel) ||
                             this.node.getComponentInChildren(BehaviorPanel);
        
        console.log('📖 LinearBranchMapExample: 组件初始化完成', {
            mainUIController: !!this.mainUIController,
            mapController: !!this._mapController,
            branchMapPanel: !!this._branchMapPanel,
            behaviorPanel: !!this._behaviorPanel
        });
    }

    /**
     * 设置事件监听器
     */
    private setupEventListeners(): void {
        const eventManager = EventManager.getInstance();
        
        // 监听地图相关事件
        eventManager.on('branch_node_clicked', this.onBranchNodeClicked, this);
        eventManager.on('map_behavior_completed', this.onMapBehaviorCompleted, this);
        eventManager.on('branch_node_unlocked', this.onBranchNodeUnlocked, this);
        eventManager.on('show_ui_message', this.onShowUIMessage, this);
        
        console.log('📖 LinearBranchMapExample: 事件监听器设置完成');
    }

    /**
     * 清理事件监听器
     */
    private cleanupEventListeners(): void {
        const eventManager = EventManager.getInstance();
        
        eventManager.off('branch_node_clicked', this.onBranchNodeClicked, this);
        eventManager.off('map_behavior_completed', this.onMapBehaviorCompleted, this);
        eventManager.off('branch_node_unlocked', this.onBranchNodeUnlocked, this);
        eventManager.off('show_ui_message', this.onShowUIMessage, this);
    }

    /**
     * 设置键盘快捷键
     */
    private setupKeyboardShortcuts(): void {
        input.on(Input.EventType.KEY_DOWN, this.onKeyDown, this);
        console.log('⌨️ LinearBranchMapExample: 键盘快捷键已启用');
    }

    /**
     * 设置按钮事件
     */
    private setupButtons(): void {
        if (this.showMapButton) {
            this.showMapButton.node.on(Button.EventType.CLICK, this.showMap, this);
        }
        
        if (this.hideMapButton) {
            this.hideMapButton.node.on(Button.EventType.CLICK, this.hideMap, this);
        }
        
        if (this.resetMapButton) {
            this.resetMapButton.node.on(Button.EventType.CLICK, this.resetMap, this);
        }
    }

    /**
     * 显示操作说明
     */
    private showInstructions(): void {
        console.log('📖 ========== 线性分支地图示例说明 ==========');
        console.log('🎮 这是一个线性分支地图系统的使用示例');
        console.log('🗺️ 功能特点:');
        console.log('   • 可拖拽的背景地图');
        console.log('   • 线性分支的交互节点');
        console.log('   • 节点间的递进解锁关系');
        console.log('   • 与行为系统的完整集成');
        console.log('⌨️ 键盘快捷键:');
        console.log('   按 M 键 - 显示/隐藏地图');
        console.log('   按 R 键 - 重置地图');
        console.log('   按 1-5 键 - 快速聚焦到不同节点');
        console.log('   按 H 键 - 显示帮助信息');
        console.log('🖱️ 鼠标操作:');
        console.log('   • 拖拽地图移动视角');
        console.log('   • 点击节点进行交互');
        console.log('   • 双指缩放地图（移动端）');
        console.log('📖 =====================================');
    }

    // ==================== 事件处理器 ====================

    /**
     * 分支节点点击事件
     */
    private onBranchNodeClicked(eventData: any): void {
        const { nodeData, behaviorType } = eventData;
        
        console.log('📖 LinearBranchMapExample: 分支节点被点击', {
            nodeName: nodeData.name,
            behaviorType: behaviorType,
            level: nodeData.level
        });
        
        // 可以在这里添加自定义的节点点击处理逻辑
        this.showNodeInfo(nodeData);
    }

    /**
     * 地图行为完成事件
     */
    private onMapBehaviorCompleted(eventData: any): void {
        const { behaviorData, nodeData } = eventData;
        
        console.log('📖 LinearBranchMapExample: 地图行为完成', {
            behaviorName: behaviorData?.name,
            nodeName: nodeData?.name
        });
        
        // 可以在这里添加完成奖励、经验值等逻辑
        this.handleBehaviorReward(nodeData);
    }

    /**
     * 分支节点解锁事件
     */
    private onBranchNodeUnlocked(eventData: any): void {
        const { nodeId, nodeData } = eventData;
        
        console.log('📖 LinearBranchMapExample: 分支节点解锁', {
            nodeId: nodeId,
            nodeName: nodeData.name,
            level: nodeData.level
        });
        
        // 可以在这里添加解锁特效、音效等
        this.playUnlockEffect(nodeData);
    }

    /**
     * UI消息显示事件
     */
    private onShowUIMessage(eventData: any): void {
        const { message, type, duration } = eventData;
        
        console.log(`📖 LinearBranchMapExample: [${type}] ${message}`);
        
        // 这里可以实现实际的UI消息显示
        // 比如Toast提示、弹窗等
    }

    /**
     * 键盘按键处理
     */
    private onKeyDown(event: EventKeyboard): void {
        switch (event.keyCode) {
            case KeyCode.KEY_M:
                this.toggleMap();
                break;
            case KeyCode.KEY_R:
                this.resetMap();
                break;
            case KeyCode.DIGIT_1:
                this.focusOnNode('start');
                break;
            case KeyCode.DIGIT_2:
                this.focusOnNode('branch_0_level_1');
                break;
            case KeyCode.DIGIT_3:
                this.focusOnNode('branch_1_level_1');
                break;
            case KeyCode.DIGIT_4:
                this.focusOnNode('branch_2_level_1');
                break;
            case KeyCode.DIGIT_5:
                this.focusOnNode('branch_0_level_3');
                break;
            case KeyCode.KEY_H:
                this.showInstructions();
                break;
        }
    }

    // ==================== 公共方法 ====================

    /**
     * 显示地图
     */
    public showMap(): void {
        if (this.mainUIController) {
            this.mainUIController.showBranchMap();
        } else if (this._branchMapPanel) {
            this._branchMapPanel.showPanel();
        }
        
        console.log('📖 LinearBranchMapExample: 显示地图');
    }

    /**
     * 隐藏地图
     */
    public hideMap(): void {
        if (this.mainUIController) {
            this.mainUIController.hideBranchMap();
        } else if (this._branchMapPanel) {
            this._branchMapPanel.hidePanel();
        }
        
        console.log('📖 LinearBranchMapExample: 隐藏地图');
    }

    /**
     * 切换地图显示状态
     */
    public toggleMap(): void {
        if (this._branchMapPanel) {
            if (this._branchMapPanel.node.active) {
                this.hideMap();
            } else {
                this.showMap();
            }
        }
    }

    /**
     * 重置地图
     */
    public resetMap(): void {
        if (this._mapController) {
            this._mapController.resetMapState();
        }
        
        console.log('📖 LinearBranchMapExample: 重置地图');
    }

    /**
     * 聚焦到指定节点
     */
    public focusOnNode(nodeId: string): void {
        if (this.mainUIController) {
            this.mainUIController.focusOnMapNode(nodeId);
        } else if (this._branchMapPanel) {
            this._branchMapPanel.focusOnNode(nodeId);
        }
        
        console.log('📖 LinearBranchMapExample: 聚焦到节点', nodeId);
    }

    // ==================== 辅助方法 ====================

    /**
     * 显示节点信息
     */
    private showNodeInfo(nodeData: any): void {
        console.log('ℹ️ 节点信息:', {
            名称: nodeData.name,
            描述: nodeData.description,
            类型: nodeData.behaviorType,
            等级: nodeData.level,
            已解锁: nodeData.unlocked
        });
    }

    /**
     * 处理行为奖励
     */
    private handleBehaviorReward(nodeData: any): void {
        if (!nodeData) return;
        
        // 这里可以实现奖励逻辑
        console.log('🎁 获得奖励:', {
            节点: nodeData.name,
            经验值: 100,
            金币: 50
        });
    }

    /**
     * 播放解锁特效
     */
    private playUnlockEffect(nodeData: any): void {
        console.log('✨ 播放解锁特效:', nodeData.name);
        
        // 这里可以实现特效播放逻辑
        // 比如粒子效果、音效、动画等
    }
}
