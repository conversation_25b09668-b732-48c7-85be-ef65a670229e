import { _decorator, Component, Button, Sprite, Label, Color, tween, Vec3, Node } from 'cc';
const { ccclass, property } = _decorator;

/**
 * 多效果按钮组件
 * 支持同时使用颜色变化和缩放效果
 * 支持将效果应用到指定的子节点上，实现Button和视觉元素的解耦
 */
@ccclass('MultiEffectButton')
export class MultiEffectButton extends Component {
    // ========== 目标节点设置 ==========
    @property({
        type: Node,
        displayName: "目标Sprite节点",
        tooltip: "应用颜色效果的Sprite节点（通常是图标）"
    })
    targetSprite: Node = null;

    @property({
        type: Node,
        displayName: "目标Label节点",
        tooltip: "应用颜色效果的Label节点（通常是文字）"
    })
    targetLabel: Node = null;

    @property({
        type: [Node],
        displayName: "缩放目标节点",
        tooltip: "应用缩放效果的节点列表（可以包含Sprite、Label等）"
    })
    scaleTargets: Node[] = [];

    // ========== 效果开关 ==========
    @property({
        displayName: "启用Sprite颜色效果",
        tooltip: "是否启用Sprite颜色变化效果"
    })
    enableSpriteColorEffect: boolean = true;

    @property({
        displayName: "启用Label颜色效果",
        tooltip: "是否启用Label颜色变化效果"
    })
    enableLabelColorEffect: boolean = true;

    @property({
        displayName: "启用缩放效果",
        tooltip: "是否启用缩放变化效果"
    })
    enableScaleEffect: boolean = true;

    // ========== Sprite颜色设置 ==========
    @property({
        displayName: "Sprite正常颜色",
        tooltip: "Sprite正常状态的颜色"
    })
    spriteNormalColor: Color = new Color(255, 255, 255, 255);

    @property({
        displayName: "Sprite按下颜色",
        tooltip: "Sprite按下时的颜色"
    })
    spritePressedColor: Color = new Color(200, 200, 200, 255);

    @property({
        displayName: "Sprite悬停颜色",
        tooltip: "Sprite悬停时的颜色"
    })
    spriteHoverColor: Color = new Color(230, 230, 230, 255);

    // ========== Label颜色设置 ==========
    @property({
        displayName: "Label正常颜色",
        tooltip: "Label正常状态的颜色"
    })
    labelNormalColor: Color = new Color(255, 255, 255, 255);

    @property({
        displayName: "Label按下颜色",
        tooltip: "Label按下时的颜色"
    })
    labelPressedColor: Color = new Color(180, 180, 180, 255);

    @property({
        displayName: "Label悬停颜色",
        tooltip: "Label悬停时的颜色"
    })
    labelHoverColor: Color = new Color(220, 220, 220, 255);

    // ========== 缩放设置 ==========
    @property({
        displayName: "正常缩放",
        tooltip: "正常状态的缩放比例"
    })
    normalScale: number = 1.0;

    @property({
        displayName: "按下缩放",
        tooltip: "按下时的缩放比例"
    })
    pressedScale: number = 0.95;

    @property({
        displayName: "悬停缩放",
        tooltip: "悬停时的缩放比例"
    })
    hoverScale: number = 1.05;

    @property({
        displayName: "动画时长",
        tooltip: "状态切换动画的持续时间（秒）"
    })
    duration: number = 0.1;

    private button: Button = null;
    private buttonComponent: any = null; // 兼容不同类型的Button组件
    private spriteComponent: Sprite = null;
    private labelComponent: Label = null;
    private originalScales: Map<Node, Vec3> = new Map();
    private isPressed: boolean = false;
    private isHovered: boolean = false;

    start() {
        // 尝试获取标准Button组件
        this.button = this.getComponent(Button);

        // 如果没有标准Button组件，尝试获取其他Button类型组件
        if (!this.button) {
            this.buttonComponent = this.findButtonComponent();
        }

        // 如果既没有标准Button也没有其他Button组件，给出警告但不阻止运行
        if (!this.button && !this.buttonComponent) {
            console.warn('MultiEffectButton: 未找到Button组件，将仅提供视觉效果，无法响应点击事件');
        }

        // 获取目标组件
        this.initializeTargetComponents();

        // 禁用Button的内置transition（如果有的话）
        this.disableButtonTransition();

        // 保存原始缩放
        this.saveOriginalScales();

        // 注册事件
        this.registerEvents();

        // 设置初始状态
        this.setNormalState();
    }

    /**
     * 查找Button类型的组件（兼容扩展Button组件）
     */
    private findButtonComponent(): any {
        const components = this.getComponents(Component);

        for (const comp of components) {
            const compName = comp.constructor.name;
            // 检查组件名称是否包含Button相关关键字
            if (compName.toLowerCase().includes('button') ||
                compName.includes('Button') ||
                compName.includes('Btn')) {
                console.log(`MultiEffectButton: 找到Button类型组件: ${compName}`);
                return comp;
            }

            // 检查组件是否有Button的典型属性
            if (comp['interactable'] !== undefined &&
                comp['transition'] !== undefined) {
                console.log(`MultiEffectButton: 找到类Button组件: ${compName}`);
                return comp;
            }
        }

        return null;
    }

    /**
     * 禁用Button组件的内置transition
     */
    private disableButtonTransition() {
        // 标准Button组件
        if (this.button && this.button.transition !== undefined) {
            this.button.transition = Button.Transition.NONE;
            console.log('MultiEffectButton: 已禁用标准Button的transition');
        }

        // 扩展Button组件
        if (this.buttonComponent && this.buttonComponent.transition !== undefined) {
            // 尝试设置为NONE（通常值为0）
            if (typeof this.buttonComponent.transition === 'number') {
                this.buttonComponent.transition = 0; // NONE
            } else if (this.buttonComponent.transition.NONE !== undefined) {
                this.buttonComponent.transition = this.buttonComponent.transition.NONE;
            }
            console.log('MultiEffectButton: 已禁用扩展Button的transition');
        }
    }

    /**
     * 初始化目标组件
     */
    private initializeTargetComponents() {
        // 获取Sprite组件
        if (this.targetSprite) {
            this.spriteComponent = this.targetSprite.getComponent(Sprite);
            if (!this.spriteComponent) {
                console.warn('目标Sprite节点没有Sprite组件');
            }
        }

        // 获取Label组件
        if (this.targetLabel) {
            this.labelComponent = this.targetLabel.getComponent(Label);
            if (!this.labelComponent) {
                console.warn('目标Label节点没有Label组件');
            }
        }

        // 如果没有指定目标节点，尝试自动查找子节点
        if (!this.targetSprite && !this.targetLabel && this.scaleTargets.length === 0) {
            this.autoFindTargets();
        }
    }

    /**
     * 自动查找目标节点
     */
    private autoFindTargets() {
        console.log('自动查找子节点...');

        for (let i = 0; i < this.node.children.length; i++) {
            const child = this.node.children[i];

            // 查找Sprite节点
            if (!this.targetSprite && child.getComponent(Sprite)) {
                this.targetSprite = child;
                this.spriteComponent = child.getComponent(Sprite);
                console.log('自动找到Sprite节点:', child.name);
            }

            // 查找Label节点
            if (!this.targetLabel && child.getComponent(Label)) {
                this.targetLabel = child;
                this.labelComponent = child.getComponent(Label);
                console.log('自动找到Label节点:', child.name);
            }

            // 添加到缩放目标
            if (this.scaleTargets.indexOf(child) === -1) {
                this.scaleTargets.push(child);
            }
        }
    }

    /**
     * 保存原始缩放值
     */
    private saveOriginalScales() {
        this.originalScales.clear();

        for (const target of this.scaleTargets) {
            if (target) {
                this.originalScales.set(target, target.scale.clone());
            }
        }
    }

    /**
     * 注册按钮事件监听
     */
    private registerEvents() {
        // 触摸事件（移动端）
        this.node.on(Node.EventType.TOUCH_START, this.onTouchStart, this);
        this.node.on(Node.EventType.TOUCH_END, this.onTouchEnd, this);
        this.node.on(Node.EventType.TOUCH_CANCEL, this.onTouchCancel, this);
        
        // 鼠标事件（PC端）
        this.node.on(Node.EventType.MOUSE_ENTER, this.onMouseEnter, this);
        this.node.on(Node.EventType.MOUSE_LEAVE, this.onMouseLeave, this);
        this.node.on(Node.EventType.MOUSE_DOWN, this.onMouseDown, this);
        this.node.on(Node.EventType.MOUSE_UP, this.onMouseUp, this);
    }

    /**
     * 触摸开始事件
     */
    private onTouchStart() {
        this.isPressed = true;
        this.setPressedState();
    }

    /**
     * 触摸结束事件
     */
    private onTouchEnd() {
        this.isPressed = false;
        if (this.isHovered) {
            this.setHoverState();
        } else {
            this.setNormalState();
        }
    }

    /**
     * 触摸取消事件
     */
    private onTouchCancel() {
        this.isPressed = false;
        this.setNormalState();
    }

    /**
     * 鼠标进入事件
     */
    private onMouseEnter() {
        this.isHovered = true;
        if (!this.isPressed) {
            this.setHoverState();
        }
    }

    /**
     * 鼠标离开事件
     */
    private onMouseLeave() {
        this.isHovered = false;
        if (!this.isPressed) {
            this.setNormalState();
        }
    }

    /**
     * 鼠标按下事件
     */
    private onMouseDown() {
        this.isPressed = true;
        this.setPressedState();
    }

    /**
     * 鼠标抬起事件
     */
    private onMouseUp() {
        this.isPressed = false;
        if (this.isHovered) {
            this.setHoverState();
        } else {
            this.setNormalState();
        }
    }

    /**
     * 设置正常状态
     */
    private setNormalState() {
        this.applyEffects(
            this.spriteNormalColor,
            this.labelNormalColor,
            this.normalScale
        );
    }

    /**
     * 设置按下状态
     */
    private setPressedState() {
        this.applyEffects(
            this.spritePressedColor,
            this.labelPressedColor,
            this.pressedScale
        );
    }

    /**
     * 设置悬停状态
     */
    private setHoverState() {
        this.applyEffects(
            this.spriteHoverColor,
            this.labelHoverColor,
            this.hoverScale
        );
    }

    /**
     * 应用视觉效果
     * @param spriteColor Sprite目标颜色
     * @param labelColor Label目标颜色
     * @param scale 目标缩放
     */
    private applyEffects(spriteColor: Color, labelColor: Color, scale: number) {
        // 停止之前的动画
        this.stopAllTweens();

        // Sprite颜色效果
        if (this.enableSpriteColorEffect && this.spriteComponent) {
            tween(this.spriteComponent)
                .to(this.duration, { color: spriteColor })
                .start();
        }

        // Label颜色效果
        if (this.enableLabelColorEffect && this.labelComponent) {
            tween(this.labelComponent)
                .to(this.duration, { color: labelColor })
                .start();
        }

        // 缩放效果
        if (this.enableScaleEffect) {
            this.applyScaleEffect(scale);
        }
    }

    /**
     * 应用缩放效果
     * @param scale 缩放比例
     */
    private applyScaleEffect(scale: number) {
        for (const target of this.scaleTargets) {
            if (!target) continue;

            const originalScale = this.originalScales.get(target);
            if (!originalScale) continue;

            const targetScale = new Vec3(
                originalScale.x * scale,
                originalScale.y * scale,
                originalScale.z
            );

            tween(target)
                .to(this.duration, { scale: targetScale })
                .start();
        }
    }

    /**
     * 停止所有动画
     */
    private stopAllTweens() {
        // 停止Sprite动画
        if (this.spriteComponent) {
            tween(this.spriteComponent).stop();
        }

        // 停止Label动画
        if (this.labelComponent) {
            tween(this.labelComponent).stop();
        }

        // 停止缩放动画
        for (const target of this.scaleTargets) {
            if (target) {
                tween(target).stop();
            }
        }
    }

    /**
     * 公共方法：手动设置按钮状态
     * @param state 状态类型：'normal' | 'pressed' | 'hover'
     */
    public setState(state: 'normal' | 'pressed' | 'hover') {
        switch (state) {
            case 'normal':
                this.setNormalState();
                break;
            case 'pressed':
                this.setPressedState();
                break;
            case 'hover':
                this.setHoverState();
                break;
        }
    }

    /**
     * 公共方法：重置到初始状态
     */
    public resetToNormal() {
        this.isPressed = false;
        this.isHovered = false;
        this.setNormalState();
    }

    /**
     * 公共方法：刷新目标组件（当动态修改目标节点时调用）
     */
    public refreshTargets() {
        this.initializeTargetComponents();
        this.saveOriginalScales();
        this.setNormalState();
    }

    /**
     * 公共方法：添加缩放目标
     * @param target 要添加的目标节点
     */
    public addScaleTarget(target: Node) {
        if (target && this.scaleTargets.indexOf(target) === -1) {
            this.scaleTargets.push(target);
            this.originalScales.set(target, target.scale.clone());
        }
    }

    /**
     * 公共方法：移除缩放目标
     * @param target 要移除的目标节点
     */
    public removeScaleTarget(target: Node) {
        const index = this.scaleTargets.indexOf(target);
        if (index !== -1) {
            this.scaleTargets.splice(index, 1);
            this.originalScales.delete(target);
        }
    }

    onDestroy() {
        // 停止所有动画
        this.stopAllTweens();

        // 清理事件监听
        this.node.off(Node.EventType.TOUCH_START, this.onTouchStart, this);
        this.node.off(Node.EventType.TOUCH_END, this.onTouchEnd, this);
        this.node.off(Node.EventType.TOUCH_CANCEL, this.onTouchCancel, this);
        this.node.off(Node.EventType.MOUSE_ENTER, this.onMouseEnter, this);
        this.node.off(Node.EventType.MOUSE_LEAVE, this.onMouseLeave, this);
        this.node.off(Node.EventType.MOUSE_DOWN, this.onMouseDown, this);
        this.node.off(Node.EventType.MOUSE_UP, this.onMouseUp, this);

        // 清理数据
        this.originalScales.clear();
    }
}
