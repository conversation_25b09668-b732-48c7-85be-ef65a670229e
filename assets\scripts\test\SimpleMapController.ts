/**
 * 简单地图控制器
 * 用于快速实现地图节点交互和行为系统集成
 */

import { _decorator, Component, Node, Button, find } from 'cc';
import { BehaviorPanel, BehaviorType } from '../ui/panels/BehaviorPanel';

const { ccclass, property } = _decorator;

@ccclass('SimpleMapController')
export class SimpleMapController extends Component {
    
    @property({ type: BehaviorPanel, tooltip: '行为面板' })
    public behaviorPanel: BehaviorPanel | null = null;
    
    @property({ type: [Node], tooltip: '交互节点列表' })
    public interactionNodes: Node[] = [];
    
    // 节点数据映射
    private _nodeDataMap: Map<string, any> = new Map();

    protected onLoad(): void {
        console.log('🎮 SimpleMapController: 简单地图控制器加载');
        this.initializeComponents();
        this.setupNodeData();
        this.bindNodeEvents();
    }

    protected onDestroy(): void {
        this.unbindNodeEvents();
    }

    /**
     * 初始化组件
     */
    private initializeComponents(): void {
        // 自动查找行为面板
        if (!this.behaviorPanel) {
            this.behaviorPanel = find('Canvas/MainUI/BehaviorPanel')?.getComponent(BehaviorPanel);
        }
        
        // 自动查找交互节点
        if (this.interactionNodes.length === 0) {
            this.autoFindInteractionNodes();
        }
        
        console.log('🎮 SimpleMapController: 组件初始化完成', {
            behaviorPanel: !!this.behaviorPanel,
            nodeCount: this.interactionNodes.length
        });
    }

    /**
     * 自动查找交互节点
     */
    private autoFindInteractionNodes(): void {
        const mapContainer = this.node.getChildByName('MapContainer');
        if (mapContainer) {
            for (let i = 0; i < mapContainer.children.length; i++) {
                const child = mapContainer.children[i];
                const button = child.getComponent(Button);
                if (button) {
                    this.interactionNodes.push(child);
                }
            }
        }
    }

    /**
     * 设置节点数据
     */
    private setupNodeData(): void {
        // 为每个节点设置基础数据
        this.interactionNodes.forEach((node, index) => {
            const nodeData = {
                id: node.name.toLowerCase(),
                name: node.name,
                description: `这是${node.name}节点`,
                behaviorType: this.getBehaviorTypeForNode(node.name),
                unlocked: index === 0, // 第一个节点默认解锁
                level: index + 1,
                rewards: {
                    exp: 100 * (index + 1),
                    gold: 50 * (index + 1)
                }
            };
            
            this._nodeDataMap.set(node.uuid, nodeData);
        });
        
        console.log('🎮 SimpleMapController: 节点数据设置完成', this._nodeDataMap.size);
    }

    /**
     * 根据节点名称获取行为类型
     */
    private getBehaviorTypeForNode(nodeName: string): BehaviorType {
        if (nodeName.includes('Start')) {
            return BehaviorType.Explore;
        } else if (nodeName.includes('Branch1')) {
            return BehaviorType.Battle;
        } else if (nodeName.includes('Branch2')) {
            return BehaviorType.Gather;
        } else if (nodeName.includes('Branch3')) {
            return BehaviorType.Study;
        }
        return BehaviorType.Explore;
    }

    /**
     * 绑定节点事件
     */
    private bindNodeEvents(): void {
        this.interactionNodes.forEach(node => {
            const button = node.getComponent(Button);
            if (button) {
                button.node.on(Button.EventType.CLICK, () => {
                    this.onNodeClicked(node);
                }, this);
            }
        });
        
        console.log('🎮 SimpleMapController: 节点事件绑定完成');
    }

    /**
     * 解绑节点事件
     */
    private unbindNodeEvents(): void {
        this.interactionNodes.forEach(node => {
            const button = node.getComponent(Button);
            if (button) {
                button.node.off(Button.EventType.CLICK);
            }
        });
    }

    /**
     * 节点点击处理
     */
    private onNodeClicked(node: Node): void {
        const nodeData = this._nodeDataMap.get(node.uuid);
        if (!nodeData) {
            console.warn('🎮 SimpleMapController: 未找到节点数据', node.name);
            return;
        }
        
        console.log('🎮 SimpleMapController: 节点被点击', nodeData.name);
        
        // 检查节点是否已解锁
        if (!nodeData.unlocked) {
            console.log('🔒 节点未解锁:', nodeData.name);
            return;
        }
        
        // 触发行为
        this.triggerBehavior(nodeData);
    }

    /**
     * 触发行为
     */
    private triggerBehavior(nodeData: any): void {
        if (!this.behaviorPanel) {
            console.warn('🎮 SimpleMapController: 行为面板未找到');
            return;
        }
        
        console.log('🎮 SimpleMapController: 触发行为', {
            node: nodeData.name,
            type: nodeData.behaviorType
        });
        
        // 创建行为数据
        const behaviorData = {
            name: `${nodeData.name}行为`,
            description: nodeData.description,
            type: nodeData.behaviorType,
            duration: 3.0,
            rewards: nodeData.rewards
        };
        
        // 执行行为
        this.behaviorPanel.executeBehavior(behaviorData);
        
        // 监听行为完成
        this.scheduleOnce(() => {
            this.onBehaviorCompleted(nodeData);
        }, behaviorData.duration + 0.5);
    }

    /**
     * 行为完成处理
     */
    private onBehaviorCompleted(nodeData: any): void {
        console.log('🎮 SimpleMapController: 行为完成', nodeData.name);
        
        // 解锁下一个节点
        this.unlockNextNode(nodeData);
        
        // 显示奖励
        this.showRewards(nodeData);
    }

    /**
     * 解锁下一个节点
     */
    private unlockNextNode(currentNodeData: any): void {
        const currentIndex = this.interactionNodes.findIndex(node => 
            this._nodeDataMap.get(node.uuid)?.id === currentNodeData.id
        );
        
        if (currentIndex >= 0 && currentIndex < this.interactionNodes.length - 1) {
            const nextNode = this.interactionNodes[currentIndex + 1];
            const nextNodeData = this._nodeDataMap.get(nextNode.uuid);
            
            if (nextNodeData && !nextNodeData.unlocked) {
                nextNodeData.unlocked = true;
                
                // 更新节点视觉状态
                this.updateNodeVisual(nextNode, true);
                
                console.log('✨ 解锁新节点:', nextNodeData.name);
            }
        }
    }

    /**
     * 更新节点视觉状态
     */
    private updateNodeVisual(node: Node, unlocked: boolean): void {
        const sprite = node.getComponent('cc.Sprite');
        if (sprite) {
            // 解锁状态下恢复原色，锁定状态下变灰
            const alpha = unlocked ? 255 : 128;
            sprite.color = sprite.color.clone();
            sprite.color.a = alpha;
        }
    }

    /**
     * 显示奖励
     */
    private showRewards(nodeData: any): void {
        console.log('🎁 获得奖励:', {
            节点: nodeData.name,
            经验值: nodeData.rewards.exp,
            金币: nodeData.rewards.gold
        });
    }

    // ==================== 公共API ====================

    /**
     * 重置地图状态
     */
    public resetMapState(): void {
        this._nodeDataMap.forEach((nodeData, uuid) => {
            nodeData.unlocked = nodeData.level === 1; // 只有第一个节点解锁
            
            const node = this.interactionNodes.find(n => n.uuid === uuid);
            if (node) {
                this.updateNodeVisual(node, nodeData.unlocked);
            }
        });
        
        console.log('🎮 SimpleMapController: 地图状态已重置');
    }

    /**
     * 解锁指定节点
     */
    public unlockNode(nodeId: string): void {
        for (const [uuid, nodeData] of this._nodeDataMap) {
            if (nodeData.id === nodeId) {
                nodeData.unlocked = true;
                
                const node = this.interactionNodes.find(n => n.uuid === uuid);
                if (node) {
                    this.updateNodeVisual(node, true);
                }
                
                console.log('🔓 手动解锁节点:', nodeData.name);
                break;
            }
        }
    }
}
