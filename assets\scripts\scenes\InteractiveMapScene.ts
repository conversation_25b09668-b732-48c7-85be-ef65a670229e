/**
 * 交互地图场景控制器
 * 演示如何使用交互地图系统
 */

import { _decorator, Component, Node, find, director, input, Input, EventKeyboard, KeyCode } from 'cc';
import { EventManager } from '../managers/EventManager';
import { InteractiveMapPanel, IInteractionNodeData } from '../ui/panels/InteractiveMapPanel';

const { ccclass, property } = _decorator;

@ccclass('InteractiveMapScene')
export class InteractiveMapScene extends Component {
    
    @property({ type: Node, tooltip: '交互地图面板节点' })
    public mapPanelNode: Node | null = null;
    
    @property({ type: Node, tooltip: 'UI根节点' })
    public uiRoot: Node | null = null;
    
    // 私有属性
    private _mapPanel: InteractiveMapPanel | null = null;
    private _currentSelectedNode: string = '';

    protected onLoad(): void {
        console.log('🗺️ InteractiveMapScene: 交互地图场景加载');
        this.initializeComponents();
        this.registerEventListeners();
        this.initializeKeyboardInput();
    }

    protected start(): void {
        console.log('🗺️ InteractiveMapScene: 交互地图场景启动');
        this.setupScene();
        this.showInstructions();
    }

    protected onDestroy(): void {
        this.unregisterEventListeners();
        input.off(Input.EventType.KEY_DOWN, this.onKeyDown, this);
    }

    /**
     * 初始化组件
     */
    private initializeComponents(): void {
        // 自动查找地图面板节点
        if (!this.mapPanelNode) {
            this.mapPanelNode = find('Canvas/InteractiveMapPanel') || 
                              find('Canvas/UI/InteractiveMapPanel') ||
                              this.node.getChildByName('InteractiveMapPanel');
        }
        
        // 获取地图面板组件
        if (this.mapPanelNode) {
            this._mapPanel = this.mapPanelNode.getComponent(InteractiveMapPanel);
            if (!this._mapPanel) {
                this._mapPanel = this.mapPanelNode.addComponent(InteractiveMapPanel);
            }
        }
        
        console.log('🗺️ InteractiveMapScene: 组件初始化完成', {
            mapPanelNode: !!this.mapPanelNode,
            mapPanel: !!this._mapPanel
        });
    }

    /**
     * 注册事件监听
     */
    private registerEventListeners(): void {
        const eventManager = EventManager.getInstance();
        
        // 监听地图节点事件
        eventManager.on('map_node_clicked', this.onMapNodeClicked, this);
        eventManager.on('map_node_unlocked', this.onMapNodeUnlocked, this);
        eventManager.on('show_node_locked_message', this.onShowNodeLockedMessage, this);
        
        // 监听不同类型节点的激活事件
        eventManager.on('action_node_activated', this.onActionNodeActivated, this);
        eventManager.on('location_node_activated', this.onLocationNodeActivated, this);
        eventManager.on('npc_node_activated', this.onNPCNodeActivated, this);
        eventManager.on('quest_node_activated', this.onQuestNodeActivated, this);
        eventManager.on('resource_node_activated', this.onResourceNodeActivated, this);
        
        console.log('🗺️ InteractiveMapScene: 事件监听注册完成');
    }

    /**
     * 取消事件监听
     */
    private unregisterEventListeners(): void {
        const eventManager = EventManager.getInstance();
        
        eventManager.off('map_node_clicked', this.onMapNodeClicked, this);
        eventManager.off('map_node_unlocked', this.onMapNodeUnlocked, this);
        eventManager.off('show_node_locked_message', this.onShowNodeLockedMessage, this);
        eventManager.off('action_node_activated', this.onActionNodeActivated, this);
        eventManager.off('location_node_activated', this.onLocationNodeActivated, this);
        eventManager.off('npc_node_activated', this.onNPCNodeActivated, this);
        eventManager.off('quest_node_activated', this.onQuestNodeActivated, this);
        eventManager.off('resource_node_activated', this.onResourceNodeActivated, this);
    }

    /**
     * 初始化键盘输入
     */
    private initializeKeyboardInput(): void {
        input.on(Input.EventType.KEY_DOWN, this.onKeyDown, this);
        console.log('⌨️ InteractiveMapScene: 键盘输入已初始化');
    }

    /**
     * 设置场景
     */
    private setupScene(): void {
        if (this._mapPanel) {
            // 显示地图面板
            this._mapPanel.showPanel();
            
            // 聚焦到起始节点
            this._mapPanel.focusOnNode('start_village');
        }
    }

    /**
     * 显示操作说明
     */
    private showInstructions(): void {
        console.log('🗺️ ========== 交互地图系统演示 ==========');
        console.log('📍 当前场景: InteractiveMap (交互地图)');
        console.log('🖱️ 拖拽地图来移动视角');
        console.log('🖱️ 点击节点进行交互');
        console.log('🔓 完成前置条件解锁新节点');
        console.log('⌨️ 键盘快捷键:');
        console.log('   按 1 键 - 聚焦到新手村');
        console.log('   按 2 键 - 聚焦到森林入口');
        console.log('   按 3 键 - 聚焦到铁匠铺');
        console.log('   按 R 键 - 重置地图位置');
        console.log('   按 + 键 - 放大地图');
        console.log('   按 - 键 - 缩小地图');
        console.log('   按 H 键 - 显示帮助信息');
        console.log('   按 ESC 键 - 返回主场景');
        console.log('🗺️ =====================================');
    }

    // ==================== 事件处理 ====================

    /**
     * 地图节点点击事件
     */
    private onMapNodeClicked(eventData: any): void {
        const { nodeData, nodeId } = eventData;
        this._currentSelectedNode = nodeId;
        
        console.log('🎯 InteractiveMapScene: 地图节点被点击', nodeData.name);
    }

    /**
     * 地图节点解锁事件
     */
    private onMapNodeUnlocked(eventData: any): void {
        const { nodeId, nodeData } = eventData;
        
        console.log('🔓 InteractiveMapScene: 节点已解锁', nodeData.name);
        
        // 可以在这里添加解锁特效、音效等
    }

    /**
     * 显示节点锁定消息
     */
    private onShowNodeLockedMessage(eventData: any): void {
        const { nodeData, prerequisites } = eventData;
        
        console.log('🔒 InteractiveMapScene: 节点被锁定', {
            nodeName: nodeData.name,
            prerequisites: prerequisites
        });
        
        // 这里可以显示UI提示
    }

    /**
     * 行动节点激活
     */
    private onActionNodeActivated(eventData: any): void {
        const { actionType, nodeData } = eventData;
        
        console.log('🎯 InteractiveMapScene: 行动节点激活', {
            actionType: actionType,
            nodeName: nodeData.name
        });
        
        // 这里可以触发具体的行动逻辑
    }

    /**
     * 位置节点激活
     */
    private onLocationNodeActivated(eventData: any): void {
        const { locationId, nodeData } = eventData;
        
        console.log('📍 InteractiveMapScene: 位置节点激活', {
            locationId: locationId,
            nodeName: nodeData.name
        });
        
        // 这里可以切换场景或显示位置信息
    }

    /**
     * NPC节点激活
     */
    private onNPCNodeActivated(eventData: any): void {
        const { npcId, nodeData } = eventData;
        
        console.log('👤 InteractiveMapScene: NPC节点激活', {
            npcId: npcId,
            nodeName: nodeData.name
        });
        
        // 这里可以打开对话界面
    }

    /**
     * 任务节点激活
     */
    private onQuestNodeActivated(eventData: any): void {
        const { questId, nodeData } = eventData;
        
        console.log('📜 InteractiveMapScene: 任务节点激活', {
            questId: questId,
            nodeName: nodeData.name
        });
        
        // 这里可以打开任务界面
    }

    /**
     * 资源节点激活
     */
    private onResourceNodeActivated(eventData: any): void {
        const { resourceType, nodeData } = eventData;
        
        console.log('💎 InteractiveMapScene: 资源节点激活', {
            resourceType: resourceType,
            nodeName: nodeData.name
        });
        
        // 这里可以执行资源收集逻辑
    }

    // ==================== 键盘输入处理 ====================

    /**
     * 键盘按键处理
     */
    private onKeyDown(event: EventKeyboard): void {
        if (!this._mapPanel) return;
        
        switch (event.keyCode) {
            case KeyCode.DIGIT_1:
                this._mapPanel.focusOnNode('start_village');
                break;
            case KeyCode.DIGIT_2:
                this._mapPanel.focusOnNode('forest_entrance');
                break;
            case KeyCode.DIGIT_3:
                this._mapPanel.focusOnNode('blacksmith');
                break;
            case KeyCode.KEY_R:
                this.resetMapView();
                break;
            case KeyCode.EQUAL: // + 键
                this.zoomIn();
                break;
            case KeyCode.MINUS: // - 键
                this.zoomOut();
                break;
            case KeyCode.KEY_H:
                this.showInstructions();
                break;
            case KeyCode.ESCAPE:
                this.returnToMainScene();
                break;
        }
    }

    /**
     * 重置地图视图
     */
    private resetMapView(): void {
        if (this._mapPanel) {
            this._mapPanel.setMapScale(1.0);
            this._mapPanel.focusOnNode('start_village');
            console.log('🗺️ InteractiveMapScene: 地图视图已重置');
        }
    }

    /**
     * 放大地图
     */
    private zoomIn(): void {
        if (this._mapPanel) {
            const currentScale = this._mapPanel.getMapScale();
            this._mapPanel.setMapScale(Math.min(2.0, currentScale + 0.2));
        }
    }

    /**
     * 缩小地图
     */
    private zoomOut(): void {
        if (this._mapPanel) {
            const currentScale = this._mapPanel.getMapScale();
            this._mapPanel.setMapScale(Math.max(0.5, currentScale - 0.2));
        }
    }

    /**
     * 返回主场景
     */
    private returnToMainScene(): void {
        console.log('🏠 InteractiveMapScene: 返回主场景');
        director.loadScene('Main');
    }

    // ==================== 公共API ====================

    /**
     * 添加自定义节点
     */
    public addCustomNode(nodeData: IInteractionNodeData): void {
        if (this._mapPanel) {
            this._mapPanel.addInteractionNode(nodeData);
        }
    }

    /**
     * 获取当前选中的节点
     */
    public getCurrentSelectedNode(): string {
        return this._currentSelectedNode;
    }
}
