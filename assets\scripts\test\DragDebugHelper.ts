/**
 * 拖拽调试助手
 * 用于监控和调试拖拽系统的状态
 */

import { _decorator, Component, Node, find, input, Input, EventKeyboard, KeyCode, Label } from 'cc';
import { DraggableMapContainer } from '../ui/components/DraggableMapContainer';

const { ccclass, property } = _decorator;

@ccclass('DragDebugHelper')
export class DragDebugHelper extends Component {
    
    @property({ tooltip: '是否启用调试' })
    public enableDebug: boolean = true;
    
    @property({ tooltip: '是否显示调试信息' })
    public showDebugInfo: boolean = true;
    
    @property({ type: Label, tooltip: '调试信息显示标签' })
    public debugLabel: Label | null = null;
    
    // 私有属性
    private _draggableContainer: DraggableMapContainer | null = null;
    private _debugUpdateTimer: number = 0;

    protected onLoad(): void {
        if (!this.enableDebug) return;
        
        console.log('🔧 DragDebugHelper: 拖拽调试助手加载');
        this.initializeComponents();
        this.setupKeyboardEvents();
        this.startDebugUpdate();
    }

    protected onDestroy(): void {
        this.cleanupKeyboardEvents();
        this.stopDebugUpdate();
    }

    /**
     * 初始化组件
     */
    private initializeComponents(): void {
        // 查找拖拽容器
        this._draggableContainer = find('Canvas/MainUI/BackPanel/LinearBranchMapContainer')?.getComponent(DraggableMapContainer);
        
        if (!this._draggableContainer) {
            console.warn('🔧 DragDebugHelper: 未找到DraggableMapContainer组件');
        }
        
        console.log('🔧 DragDebugHelper: 组件初始化完成', {
            draggableContainer: !!this._draggableContainer,
            debugLabel: !!this.debugLabel
        });
    }

    /**
     * 设置键盘事件
     */
    private setupKeyboardEvents(): void {
        input.on(Input.EventType.KEY_DOWN, this.onKeyDown, this);
        console.log('🔧 DragDebugHelper: 键盘事件设置完成');
    }

    /**
     * 清理键盘事件
     */
    private cleanupKeyboardEvents(): void {
        input.off(Input.EventType.KEY_DOWN, this.onKeyDown, this);
    }

    /**
     * 键盘按键处理
     */
    private onKeyDown(event: EventKeyboard): void {
        if (!this.enableDebug) return;

        switch (event.keyCode) {
            case KeyCode.KEY_D:
                this.toggleDebugInfo();
                break;
            case KeyCode.KEY_F:
                this.forceResetDragState();
                break;
            case KeyCode.KEY_I:
                this.showDragInfo();
                break;
            case KeyCode.KEY_C:
                this.clearDebugLog();
                break;
            case KeyCode.KEY_E:
                this.emergencyReset();
                break;
            case KeyCode.KEY_M:
                this.monitorTouchEvents();
                break;
            case KeyCode.KEY_S:
                this.interruptInertia();
                break;
        }
    }

    /**
     * 开始调试信息更新
     */
    private startDebugUpdate(): void {
        if (!this.showDebugInfo) return;
        
        this._debugUpdateTimer = setInterval(() => {
            this.updateDebugInfo();
        }, 500); // 每0.5秒更新一次
    }

    /**
     * 停止调试信息更新
     */
    private stopDebugUpdate(): void {
        if (this._debugUpdateTimer) {
            clearInterval(this._debugUpdateTimer);
            this._debugUpdateTimer = 0;
        }
    }

    /**
     * 更新调试信息
     */
    private updateDebugInfo(): void {
        if (!this.debugLabel || !this._draggableContainer) return;
        
        const container = this._draggableContainer;
        const position = container.node.getPosition();
        const scale = container.node.getScale();
        
        const debugText = `拖拽调试信息:
位置: (${position.x.toFixed(1)}, ${position.y.toFixed(1)})
缩放: ${scale.x.toFixed(2)}
状态: ${this.getDragStateText()}

快捷键:
D - 切换调试显示
F - 强制重置状态
I - 显示详细信息
C - 清理调试日志`;
        
        this.debugLabel.string = debugText;
    }

    /**
     * 获取拖拽状态文本
     */
    private getDragStateText(): string {
        if (!this._draggableContainer) return '未知';
        
        // 这里需要访问私有属性，实际使用时可能需要在DraggableMapContainer中添加公共getter方法
        return '正常'; // 简化显示
    }

    /**
     * 切换调试信息显示
     */
    private toggleDebugInfo(): void {
        this.showDebugInfo = !this.showDebugInfo;
        
        if (this.debugLabel) {
            this.debugLabel.node.active = this.showDebugInfo;
        }
        
        if (this.showDebugInfo) {
            this.startDebugUpdate();
        } else {
            this.stopDebugUpdate();
        }
        
        console.log('🔧 DragDebugHelper: 调试信息显示', this.showDebugInfo ? '开启' : '关闭');
    }

    /**
     * 强制重置拖拽状态
     */
    private forceResetDragState(): void {
        if (!this._draggableContainer) {
            console.warn('🔧 DragDebugHelper: 拖拽容器不存在，无法重置状态');
            return;
        }

        console.log('🔧 DragDebugHelper: 强制重置拖拽状态');
        this._draggableContainer.resetDragState();

        // 显示重置确认
        if (this.debugLabel) {
            this.debugLabel.string = '状态已重置！';
            this.scheduleOnce(() => {
                this.updateDebugInfo();
            }, 1.0);
        }
    }

    /**
     * 打断惯性动画
     */
    private interruptInertia(): void {
        if (!this._draggableContainer) {
            console.warn('🔧 DragDebugHelper: 拖拽容器不存在，无法打断惯性动画');
            return;
        }

        console.log('🔧 DragDebugHelper: 打断惯性动画');
        this._draggableContainer.interruptInertia();

        // 显示打断确认
        if (this.debugLabel) {
            this.debugLabel.string = '惯性动画已打断！';
            this.scheduleOnce(() => {
                this.updateDebugInfo();
            }, 1.0);
        }
    }

    /**
     * 显示拖拽详细信息
     */
    private showDragInfo(): void {
        if (!this._draggableContainer) {
            console.warn('🔧 DragDebugHelper: 拖拽容器不存在');
            return;
        }
        
        const container = this._draggableContainer;
        const position = container.node.getPosition();
        const scale = container.node.getScale();
        
        console.log('🔧 ========== 拖拽系统详细信息 ==========');
        console.log('📍 当前位置:', position);
        console.log('🔍 当前缩放:', scale);
        console.log('🎮 节点名称:', container.node.name);
        console.log('⚙️ 组件状态: 正常');
        console.log('🔧 =====================================');
    }

    /**
     * 清理调试日志
     */
    private clearDebugLog(): void {
        console.clear();
        console.log('🔧 DragDebugHelper: 调试日志已清理');
        this.showDebugInstructions();
    }

    /**
     * 显示调试说明
     */
    private showDebugInstructions(): void {
        console.log('🔧 ========== 拖拽调试助手说明 ==========');
        console.log('🎯 用于监控和调试拖拽系统状态');
        console.log('⌨️ 快捷键:');
        console.log('   D键 - 切换调试信息显示');
        console.log('   F键 - 强制重置拖拽状态（解决卡住问题）');
        console.log('   I键 - 显示详细拖拽信息');
        console.log('   C键 - 清理调试日志');
        console.log('🐛 常见问题解决:');
        console.log('   • 拖拽卡住 → 按F键强制重置');
        console.log('   • 缩放异常 → 按F键强制重置');
        console.log('   • 状态异常 → 按I键查看详细信息');
        console.log('🔧 =====================================');
    }

    // ==================== 公共API ====================

    /**
     * 手动重置拖拽状态
     */
    public resetDragState(): void {
        this.forceResetDragState();
    }

    /**
     * 获取拖拽容器引用
     */
    public getDraggableContainer(): DraggableMapContainer | null {
        return this._draggableContainer;
    }

    /**
     * 设置调试模式
     */
    public setDebugMode(enabled: boolean): void {
        this.enableDebug = enabled;
        
        if (!enabled) {
            this.stopDebugUpdate();
            if (this.debugLabel) {
                this.debugLabel.node.active = false;
            }
        } else {
            this.showDebugInstructions();
        }
    }
}
