/**
 * 主界面控制器
 * 管理底部按钮和界面切换
 */

import { _decorator, Component, Node, Button, Prefab, instantiate, Canvas, find } from 'cc';
import { UIManager } from '../managers/UIManager';
import { UIPanelType } from './types/UITypes';
import { LinearBranchMapPanel } from './panels/LinearBranchMapPanel';
import { InteractiveMapController } from './controllers/InteractiveMapController';
import { EventManager } from '../managers/EventManager';
import { BehaviorPanel, BehaviorType } from './panels/BehaviorPanel';
import { DraggableMapContainer } from './components/DraggableMapContainer';

const { ccclass, property } = _decorator;

@ccclass('MainUIController')
export class MainUIController extends Component {
    
    // 底部按钮引用
    @property({ type: Button, tooltip: '角色按钮' })
    public userButton: Button = null!;
    
    @property({ type: Button, tooltip: '背包按钮' })
    public bagButton: Button = null!;
    
    @property({ type: Button, tooltip: '商店按钮' })
    public shopButton: Button = null!;
    
    @property({ type: Button, tooltip: '其他按钮' })
    public elseButton: Button = null!;
    
    // 预制件引用
    @property({ type: Prefab, tooltip: '角色界面预制件' })
    public characterPanelPrefab: Prefab = null!;
    
    @property({ type: Prefab, tooltip: '背包界面预制件' })
    public bagPanelPrefab: Prefab = null!;
    
    @property({ type: Prefab, tooltip: '商店界面预制件' })
    public shopPanelPrefab: Prefab = null!;

    // 地图相关组件
    @property({ type: LinearBranchMapPanel, tooltip: '线性分支地图面板' })
    public branchMapPanel: LinearBranchMapPanel | null = null;

    @property({ type: InteractiveMapController, tooltip: '交互地图控制器' })
    public mapController: InteractiveMapController | null = null;

    @property({ type: Node, tooltip: '地图容器节点' })
    public mapContainer: Node | null = null;

    // UI管理器引用
    private uiManager: UIManager = null!;
    
    // 当前打开的界面实例
    private currentPanel: Node | null = null;
    
    // Canvas引用（用于添加界面）
    private canvas: Canvas = null!;

    start() {
        this.initializeComponents();
        this.bindButtonEvents();
        this.initializeMapSystem();
        console.log('🎮 主界面控制器初始化完成');
    }

    onDestroy() {
        this.unbindButtonEvents();
    }

    /**
     * 初始化组件引用
     */
    private initializeComponents(): void {
        // 获取UIManager实例
        this.uiManager = UIManager.getInstance();
        
        // 获取Canvas组件
        this.canvas = this.node.getComponentInParent(Canvas);
        if (!this.canvas) {
            console.error('❌ 找不到Canvas组件');
        }
        
        // 如果没有手动指定按钮，尝试自动查找
        this.autoFindButtons();
        
        console.log('📋 组件引用初始化完成');
    }

    /**
     * 自动查找按钮
     */
    private autoFindButtons(): void {
        if (!this.userButton) {
            const userNode = this.node.getChildByPath('BottomPanel/BottonIcon/User/Button');
            if (userNode) {
                this.userButton = userNode.getComponent(Button);
            }
        }
        
        if (!this.bagButton) {
            const bagNode = this.node.getChildByPath('BottomPanel/BottonIcon/Bag/Button');
            if (bagNode) {
                this.bagButton = bagNode.getComponent(Button);
            }
        }
        
        if (!this.shopButton) {
            const shopNode = this.node.getChildByPath('BottomPanel/BottonIcon/Shop/Button');
            if (shopNode) {
                this.shopButton = shopNode.getComponent(Button);
            }
        }
        
        if (!this.elseButton) {
            const elseNode = this.node.getChildByPath('BottomPanel/BottonIcon/Else/Button');
            if (elseNode) {
                this.elseButton = elseNode.getComponent(Button);
            }
        }
        
        console.log('🔍 自动查找按钮完成');
    }

    /**
     * 初始化地图系统
     */
    private initializeMapSystem(): void {
        // 自动查找地图容器
        if (!this.mapContainer) {
            this.mapContainer = this.node.getChildByPath('MapPanel') ||
                              this.node.getChildByPath('BehaviorPanel/MapContainer');
        }

        // 自动查找或创建线性分支地图面板
        if (!this.branchMapPanel && this.mapContainer) {
            this.branchMapPanel = this.mapContainer.getComponent(LinearBranchMapPanel);
            if (!this.branchMapPanel) {
                // 如果没有找到，可以动态创建
                console.log('🗺️ 未找到LinearBranchMapPanel组件，可以在需要时动态创建');
            }
        }

        // 自动查找或创建交互地图控制器
        if (!this.mapController) {
            this.mapController = this.node.getComponent(InteractiveMapController);
            if (!this.mapController && this.mapContainer) {
                this.mapController = this.mapContainer.getComponent(InteractiveMapController);
            }
        }

        // 配置地图控制器
        if (this.mapController && this.branchMapPanel) {
            this.mapController.branchMapPanel = this.branchMapPanel;
            console.log('🗺️ 地图系统初始化完成');
        }

        // 绑定地图相关事件
        this.bindMapEvents();
    }

    /**
     * 绑定地图相关事件
     */
    private bindMapEvents(): void {
        const eventManager = EventManager.getInstance();

        // 监听地图节点激活事件
        eventManager.on('map_node_activated', this.onMapNodeActivated, this);

        // 监听地图行为完成事件
        eventManager.on('map_behavior_completed', this.onMapBehaviorCompleted, this);

        console.log('🗺️ 地图事件绑定完成');
    }

    /**
     * 绑定按钮事件
     */
    private bindButtonEvents(): void {
        if (this.userButton) {
            this.userButton.node.on(Button.EventType.CLICK, this.onUserButtonClick, this);
            console.log('✅ 角色按钮事件绑定成功');
        }
        
        if (this.bagButton) {
            this.bagButton.node.on(Button.EventType.CLICK, this.onBagButtonClick, this);
            console.log('✅ 背包按钮事件绑定成功');
        }
        
        if (this.shopButton) {
            this.shopButton.node.on(Button.EventType.CLICK, this.onShopButtonClick, this);
            console.log('✅ 商店按钮事件绑定成功');
        }
        
        if (this.elseButton) {
            this.elseButton.node.on(Button.EventType.CLICK, this.onElseButtonClick, this);
            console.log('✅ 其他按钮事件绑定成功');
        }
    }

    /**
     * 解绑按钮事件
     */
    private unbindButtonEvents(): void {
        if (this.userButton) {
            this.userButton.node.off(Button.EventType.CLICK, this.onUserButtonClick, this);
        }

        if (this.bagButton) {
            this.bagButton.node.off(Button.EventType.CLICK, this.onBagButtonClick, this);
        }

        if (this.shopButton) {
            this.shopButton.node.off(Button.EventType.CLICK, this.onShopButtonClick, this);
        }

        if (this.elseButton) {
            this.elseButton.node.off(Button.EventType.CLICK, this.onElseButtonClick, this);
        }

        // 解绑地图事件
        this.unbindMapEvents();
    }

    /**
     * 解绑地图相关事件
     */
    private unbindMapEvents(): void {
        const eventManager = EventManager.getInstance();

        eventManager.off('map_node_activated', this.onMapNodeActivated, this);
        eventManager.off('map_behavior_completed', this.onMapBehaviorCompleted, this);

        console.log('🗺️ 地图事件解绑完成');
    }

    /**
     * 角色按钮点击事件
     */
    private onUserButtonClick(): void {
        console.log('🎯 点击角色按钮');
        this.openCharacterPanel();
    }

    /**
     * 背包按钮点击事件
     */
    private onBagButtonClick(): void {
        console.log('🎯 点击背包按钮');
        this.openBagPanel();
    }

    /**
     * 商店按钮点击事件
     */
    private onShopButtonClick(): void {
        console.log('🎯 点击商店按钮');
        this.openShopPanel();
    }

    /**
     * 其他按钮点击事件
     */
    private onElseButtonClick(): void {
        console.log('🎯 点击其他按钮');
        // 可以在这里添加其他功能
    }

    /**
     * 打开角色界面
     */
    private openCharacterPanel(): void {
        if (this.uiManager) {
            // 使用UIManager打开界面
            this.uiManager.showPanel(UIPanelType.Character);
        } else {
            // 直接实例化预制件的方式
            this.openPanelByPrefab(this.characterPanelPrefab, '角色界面');
        }
    }

    /**
     * 打开背包界面
     */
    private openBagPanel(): void {
        if (this.uiManager) {
            this.uiManager.showPanel(UIPanelType.Inventory);
        } else {
            this.openPanelByPrefab(this.bagPanelPrefab, '背包界面');
        }
    }

    /**
     * 打开商店界面
     */
    private openShopPanel(): void {
        if (this.uiManager) {
            this.uiManager.showPanel(UIPanelType.Shop);
        } else {
            this.openPanelByPrefab(this.shopPanelPrefab, '商店界面');
        }
    }

    /**
     * 通过预制件打开界面（备用方案）
     */
    private openPanelByPrefab(prefab: Prefab, panelName: string): void {
        if (!prefab) {
            console.warn(`❌ ${panelName}预制件未设置`);
            return;
        }
        
        // 关闭当前界面
        this.closeCurrentPanel();
        
        // 实例化新界面
        const panelNode = instantiate(prefab);
        panelNode.setParent(this.canvas.node);
        
        // 设置界面层级（确保在最上层）
        panelNode.setSiblingIndex(-1);
        
        // 记录当前界面
        this.currentPanel = panelNode;
        
        // 绑定关闭事件
        this.bindPanelCloseEvent(panelNode);
        
        console.log(`✅ ${panelName}打开成功`);
    }

    /**
     * 绑定界面关闭事件
     */
    private bindPanelCloseEvent(panelNode: Node): void {
        // 查找关闭按钮
        const closeButton = panelNode.getComponentInChildren(Button);
        if (closeButton && closeButton.node.name.includes('Close')) {
            closeButton.node.on(Button.EventType.CLICK, () => {
                this.closeCurrentPanel();
            }, this);
        }
        
        // 也可以监听自定义关闭事件
        panelNode.on('panel-close', () => {
            this.closeCurrentPanel();
        }, this);
    }

    /**
     * 关闭当前界面
     */
    private closeCurrentPanel(): void {
        if (this.currentPanel && this.currentPanel.isValid) {
            this.currentPanel.destroy();
            this.currentPanel = null;
            console.log('✅ 界面关闭成功');
        }
    }

    /**
     * 检查界面是否已打开
     */
    public isPanelOpen(): boolean {
        return this.currentPanel !== null && this.currentPanel.isValid;
    }

    /**
     * 获取当前打开的界面
     */
    public getCurrentPanel(): Node | null {
        return this.currentPanel;
    }

    /**
     * 强制关闭所有界面
     */
    public closeAllPanels(): void {
        this.closeCurrentPanel();

        if (this.uiManager) {
            // 如果使用UIManager，也关闭其管理的界面
            this.uiManager.hidePanel(UIPanelType.Character);
            this.uiManager.hidePanel(UIPanelType.Inventory);
            this.uiManager.hidePanel(UIPanelType.Shop);
        }
    }

    // ==================== 地图事件处理 ====================

    /**
     * 地图节点激活事件处理
     */
    private onMapNodeActivated(eventData: any): void {
        const { nodeData, behaviorType } = eventData;

        console.log('🗺️ MainUIController: 地图节点激活', nodeData.name, behaviorType);

        // 可以在这里添加UI反馈，比如显示节点信息
        // 或者更新底部按钮状态等
    }

    /**
     * 地图行为完成事件处理
     */
    private onMapBehaviorCompleted(eventData: any): void {
        const { behaviorData, nodeData } = eventData;

        console.log('🗺️ MainUIController: 地图行为完成', behaviorData?.name);

        // 可以在这里添加完成反馈，比如显示奖励信息
        // 或者更新UI状态等
    }

    // ==================== 地图系统公共API ====================

    /**
     * 显示线性分支地图
     */
    public showBranchMap(): void {
        if (this.branchMapPanel) {
            this.branchMapPanel.showPanel();
            console.log('🗺️ MainUIController: 显示线性分支地图');
        } else {
            console.warn('🗺️ MainUIController: 线性分支地图面板未找到');
        }
    }

    /**
     * 隐藏线性分支地图
     */
    public hideBranchMap(): void {
        if (this.branchMapPanel) {
            this.branchMapPanel.hidePanel();
            console.log('🗺️ MainUIController: 隐藏线性分支地图');
        }
    }

    /**
     * 聚焦到指定地图节点
     */
    public focusOnMapNode(nodeId: string): void {
        if (this.mapController) {
            this.mapController.focusOnNode(nodeId);
            console.log('🗺️ MainUIController: 聚焦到地图节点', nodeId);
        }
    }

    /**
     * 获取地图控制器
     */
    public getMapController(): InteractiveMapController | null {
        return this.mapController;
    }

    /**
     * 获取线性分支地图面板
     */
    public getBranchMapPanel(): LinearBranchMapPanel | null {
        return this.branchMapPanel;
    }

    // ==================== 地图节点点击处理 ====================

    /**
     * 地图节点点击处理
     */
    public onMapNodeClick(event: any, customEventData: string): void {
        console.log('🗺️ MainUIController: 地图节点被点击', customEventData);

        // 检查是否正在拖拽地图，如果是则忽略点击
        if (DraggableMapContainer.isInputBlocked()) {
            console.log('🚫 MainUIController: 地图正在拖拽，忽略节点点击');
            return;
        }

        // 获取行为面板
        const behaviorPanel = find('Canvas/MainUI/BehaviorPanel')?.getComponent(BehaviorPanel);
        if (!behaviorPanel) {
            console.warn('🗺️ MainUIController: 未找到行为面板');
            return;
        }

        // 根据节点ID获取行为类型和数据
        const nodeData = this.getNodeDataById(customEventData);
        if (!nodeData) {
            console.warn('🗺️ MainUIController: 未找到节点数据', customEventData);
            return;
        }

        // 检查节点是否已解锁
        if (!nodeData.unlocked) {
            console.log('🔒 节点未解锁:', nodeData.name);
            this.showLockedMessage(nodeData.name);
            return;
        }

        // 触发行为
        this.triggerNodeBehavior(behaviorPanel, nodeData);
    }

    /**
     * 根据节点ID获取节点数据
     */
    private getNodeDataById(nodeId: string): any {
        const nodeDataMap = {
            'start': {
                id: 'start',
                name: '起始点',
                description: '探索的起点',
                behaviorType: BehaviorType.Move,
                unlocked: true,
                level: 1,
                duration: 2.0
            },
            'branchnode1': {
                id: 'branchnode1',
                name: '战斗区域',
                description: '进行战斗训练',
                behaviorType: BehaviorType.Attack,
                unlocked: false,
                level: 2,
                duration: 3.0
            },
            'branchnode2': {
                id: 'branchnode2',
                name: '采集区域',
                description: '收集资源',
                behaviorType: BehaviorType.Item,
                unlocked: false,
                level: 2,
                duration: 2.5
            },
            'branchnode3': {
                id: 'branchnode3',
                name: '学习区域',
                description: '提升技能',
                behaviorType: BehaviorType.Skill,
                unlocked: false,
                level: 3,
                duration: 4.0
            }
        };

        return nodeDataMap[nodeId] || null;
    }

    /**
     * 显示锁定消息
     */
    private showLockedMessage(nodeName: string): void {
        console.log('🔒 ' + nodeName + ' 尚未解锁，请先完成前置任务');
        // 这里可以添加UI提示
    }

    /**
     * 触发节点行为
     */
    private triggerNodeBehavior(behaviorPanel: BehaviorPanel, nodeData: any): void {
        console.log('🗺️ MainUIController: 触发节点行为', nodeData.name);

        // 创建行为数据
        const customBehaviorData = {
            name: nodeData.name + '任务',
            description: nodeData.description,
            duration: nodeData.duration,
            interruptible: true
        };

        // 执行行为 - 使用BehaviorPanel的接口
        behaviorPanel.executeBehavior(nodeData.behaviorType, customBehaviorData);

        // 监听行为完成
        this.scheduleOnce(() => {
            this.onNodeBehaviorCompleted(nodeData);
        }, nodeData.duration + 0.5);
    }

    /**
     * 节点行为完成处理
     */
    private onNodeBehaviorCompleted(nodeData: any): void {
        console.log('🗺️ MainUIController: 节点行为完成', nodeData.name);

        // 显示奖励
        console.log('🎁 完成任务获得奖励:', {
            经验值: 100 * nodeData.level,
            金币: 50 * nodeData.level
        });

        // 解锁下一个节点
        this.unlockNextNodes(nodeData.id);
    }

    /**
     * 解锁下一个节点
     */
    private unlockNextNodes(completedNodeId: string): void {
        // 根据完成的节点解锁相应的下一个节点
        if (completedNodeId === 'start') {
            // 完成起始点后解锁所有分支节点
            console.log('✨ 解锁分支节点: 战斗区域、采集区域');
            // 这里可以添加实际的解锁逻辑
        } else if (completedNodeId === 'branchnode1' || completedNodeId === 'branchnode2') {
            // 完成任一分支后解锁学习区域
            console.log('✨ 解锁高级区域: 学习区域');
            // 这里可以添加实际的解锁逻辑
        }
    }
}
