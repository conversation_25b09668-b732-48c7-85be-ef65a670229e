/**
 * 可拖拽地图容器组件
 * 提供地图的拖拽、缩放和边界限制功能
 */

import { _decorator, Component, Node, Vec3, Vec2, input, Input, EventTouch, UITransform, tween, Tween } from 'cc';

const { ccclass, property } = _decorator;

/**
 * 拖拽配置接口
 */
export interface IDragConfig {
    /** 是否启用拖拽 */
    enableDrag: boolean;
    
    /** 是否启用缩放 */
    enableZoom: boolean;
    
    /** 拖拽阻尼系数 */
    dragDamping: number;
    
    /** 缩放敏感度 */
    zoomSensitivity: number;
    
    /** 最小缩放 */
    minScale: number;
    
    /** 最大缩放 */
    maxScale: number;
    
    /** 拖拽边界 */
    bounds: {
        left: number;
        right: number;
        top: number;
        bottom: number;
    };

    /** 启用边界限制 */
    enableBounds: boolean;

    /** 是否启用惯性 */
    enableInertia: boolean;
    
    /** 惯性阻尼 */
    inertiaDamping: number;
}

@ccclass('DraggableMapContainer')
export class DraggableMapContainer extends Component {
    
    @property({ tooltip: '是否启用拖拽' })
    public enableDrag: boolean = true;
    
    @property({ tooltip: '是否启用缩放' })
    public enableZoom: boolean = true;
    
    @property({ tooltip: '拖拽阻尼系数', range: [0.1, 1.0] })
    public dragDamping: number = 0.8;
    
    @property({ tooltip: '缩放敏感度', range: [0.01, 1.0] })
    public zoomSensitivity: number = 0.1;
    
    @property({ tooltip: '最小缩放', range: [0.1, 1.0] })
    public minScale: number = 0.5;
    
    @property({ tooltip: '最大缩放', range: [1.0, 5.0] })
    public maxScale: number = 2.0;
    
    @property({ tooltip: '是否启用惯性' })
    public enableInertia: boolean = true;
    
    @property({ tooltip: '惯性阻尼', range: [0.1, 1.0] })
    public inertiaDamping: number = 0.95;
    
    // 私有属性
    private _config: IDragConfig = {
        enableDrag: true,
        enableZoom: true,
        dragDamping: 0.8,
        zoomSensitivity: 0.1,
        minScale: 0.5,
        maxScale: 2.0,
        bounds: {
            left: -500,
            right: 500,
            top: 300,
            bottom: -300
        },
        enableBounds: true,
        enableInertia: true,
        inertiaDamping: 0.95
    };
    
    // 拖拽状态
    private _isDragging: boolean = false;
    private _lastTouchPosition: Vec2 = new Vec2();
    private _dragStartPosition: Vec2 = new Vec2();
    private _velocity: Vec2 = new Vec2();
    private _lastMoveTime: number = 0;
    
    // 缩放状态
    private _isZooming: boolean = false;
    private _lastTouchDistance: number = 0;
    
    // 惯性动画
    private _inertiaTween: Tween<Node> | null = null;

    // 状态检查定时器
    private _stateCheckTimer: number = 0;

    // 按钮输入屏蔽
    private static _isBlockingInput: boolean = false;

    protected onLoad(): void {
        console.log('🎮 DraggableMapContainer: 可拖拽地图容器加载');
        this.updateConfig();
        this.setupInputEvents();
        this.startStateCheck();
    }

    protected onDestroy(): void {
        this.cleanupInputEvents();
        this.stopInertia();
        this.stopStateCheck();
    }

    /**
     * 更新配置
     */
    private updateConfig(): void {
        this._config.enableDrag = this.enableDrag;
        this._config.enableZoom = this.enableZoom;
        this._config.dragDamping = this.dragDamping;
        this._config.zoomSensitivity = this.zoomSensitivity;
        this._config.minScale = this.minScale;
        this._config.maxScale = this.maxScale;
        this._config.enableInertia = this.enableInertia;
        this._config.inertiaDamping = this.inertiaDamping;
    }

    /**
     * 设置输入事件
     */
    private setupInputEvents(): void {
        // 使用全局输入监听，避免被子节点拦截
        input.on(Input.EventType.TOUCH_START, this.onGlobalTouchStart, this);
        input.on(Input.EventType.TOUCH_MOVE, this.onGlobalTouchMove, this);
        input.on(Input.EventType.TOUCH_END, this.onGlobalTouchEnd, this);
        input.on(Input.EventType.TOUCH_CANCEL, this.onGlobalTouchEnd, this);

        // 保留节点级别监听作为备用
        this.node.on(Input.EventType.TOUCH_START, this.onTouchStart, this);
        this.node.on(Input.EventType.TOUCH_MOVE, this.onTouchMove, this);
        this.node.on(Input.EventType.TOUCH_END, this.onTouchEnd, this);
        this.node.on(Input.EventType.TOUCH_CANCEL, this.onTouchEnd, this);

        console.log('🎮 DraggableMapContainer: 输入事件设置完成（全局+节点双重监听）');
    }

    /**
     * 清理输入事件
     */
    private cleanupInputEvents(): void {
        // 清理全局监听
        input.off(Input.EventType.TOUCH_START, this.onGlobalTouchStart, this);
        input.off(Input.EventType.TOUCH_MOVE, this.onGlobalTouchMove, this);
        input.off(Input.EventType.TOUCH_END, this.onGlobalTouchEnd, this);
        input.off(Input.EventType.TOUCH_CANCEL, this.onGlobalTouchEnd, this);

        // 清理节点监听
        this.node.off(Input.EventType.TOUCH_START, this.onTouchStart, this);
        this.node.off(Input.EventType.TOUCH_MOVE, this.onTouchMove, this);
        this.node.off(Input.EventType.TOUCH_END, this.onTouchEnd, this);
        this.node.off(Input.EventType.TOUCH_CANCEL, this.onTouchEnd, this);
    }

    /**
     * 触摸开始
     */
    private onTouchStart(event: EventTouch): void {
        if (!this._config.enableDrag) return;

        const touches = event.getAllTouches();

        // 立即停止惯性动画和重置状态
        this.forceStopInertia();
        this.forceResetStates();

        console.log('🎮 DraggableMapContainer: 触摸开始，强制停止惯性动画');

        if (touches.length === 1) {
            // 单点触摸 - 拖拽
            this.startDrag(event);
        } else if (touches.length === 2 && this._config.enableZoom) {
            // 双点触摸 - 缩放
            this.startZoom(touches);
        }
    }

    /**
     * 触摸移动
     */
    private onTouchMove(event: EventTouch): void {
        const touches = event.getAllTouches();
        
        if (touches.length === 1 && this._isDragging) {
            // 单点拖拽
            this.updateDrag(event);
        } else if (touches.length === 2 && this._isZooming) {
            // 双点缩放
            this.updateZoom(touches);
        }
    }

    /**
     * 触摸结束
     */
    private onTouchEnd(event: EventTouch): void {
        const touches = event.getAllTouches();

        try {
            if (touches.length === 0) {
                // 所有触摸结束
                if (this._isDragging) {
                    this.endDrag();
                }
                if (this._isZooming) {
                    this.endZoom();
                }
                // 确保状态完全重置
                this.scheduleOnce(() => {
                    this.resetStates();
                }, 0.1);
            } else if (touches.length === 1) {
                // 从双点变为单点
                if (this._isZooming) {
                    this.endZoom();
                    this.startDrag(event);
                }
            }
        } catch (error) {
            console.warn('🎮 DraggableMapContainer: 触摸结束处理异常', error);
            this.forceResetStates();
        }
    }

    /**
     * 开始拖拽
     */
    private startDrag(event: EventTouch): void {
        // 确保完全清理之前的状态
        this.forceStopInertia();
        this.forceResetStates();

        this._isDragging = true;
        this._lastTouchPosition = event.getUILocation();
        this._dragStartPosition.set(this._lastTouchPosition);
        this._velocity.set(0, 0);
        this._lastMoveTime = Date.now();

        // 屏蔽按钮输入
        DraggableMapContainer.setInputBlocking(true);

        console.log('🎮 DraggableMapContainer: 开始拖拽，屏蔽按钮输入');
    }

    /**
     * 更新拖拽
     */
    private updateDrag(event: EventTouch): void {
        const currentPosition = event.getUILocation();
        const deltaX = (currentPosition.x - this._lastTouchPosition.x) * this._config.dragDamping;
        const deltaY = (currentPosition.y - this._lastTouchPosition.y) * this._config.dragDamping;

        // 更新位置（2D游戏只需要X,Y）
        const currentNodePosition = this.node.getPosition();
        const newPosition = new Vec3(
            currentNodePosition.x + deltaX,
            currentNodePosition.y + deltaY,
            currentNodePosition.z
        );
        
        // 应用边界限制
        this.applyBounds(newPosition);
        this.node.setPosition(newPosition);
        
        // 计算速度（用于惯性）
        const currentTime = Date.now();
        const deltaTime = (currentTime - this._lastMoveTime) / 1000;
        if (deltaTime > 0) {
            this._velocity.set(deltaX / deltaTime, deltaY / deltaTime);
        }
        
        this._lastTouchPosition = currentPosition;
        this._lastMoveTime = currentTime;
    }

    /**
     * 结束拖拽
     */
    private endDrag(): void {
        this._isDragging = false;

        // 启动惯性动画
        if (this._config.enableInertia && this._velocity.length() > 50) {
            this.startInertia();
        } else {
            // 如果没有惯性动画，立即解除输入屏蔽
            DraggableMapContainer.setInputBlocking(false);
        }

        console.log('🎮 DraggableMapContainer: 结束拖拽');
    }

    /**
     * 开始缩放
     */
    private startZoom(touches: any[]): void {
        this._isZooming = true;
        this._lastTouchDistance = this.getTouchDistance(touches);
        
        console.log('🎮 DraggableMapContainer: 开始缩放');
    }

    /**
     * 更新缩放
     */
    private updateZoom(touches: any[]): void {
        const currentDistance = this.getTouchDistance(touches);
        const deltaDistance = currentDistance - this._lastTouchDistance;
        
        if (Math.abs(deltaDistance) > 5) { // 防止微小变化
            const scaleChange = deltaDistance * this._config.zoomSensitivity * 0.01;
            const currentScale = this.node.getScale().x;
            const newScale = Math.max(this._config.minScale, 
                            Math.min(this._config.maxScale, currentScale + scaleChange));
            
            this.node.setScale(newScale, newScale, 1);
            this._lastTouchDistance = currentDistance;
        }
    }

    /**
     * 结束缩放
     */
    private endZoom(): void {
        this._isZooming = false;
        console.log('🎮 DraggableMapContainer: 结束缩放');
    }

    // ==================== 全局触摸事件处理 ====================

    /**
     * 全局触摸开始
     */
    private onGlobalTouchStart(event: EventTouch): void {
        const touchPos = event.getLocation();
        const worldPos = new Vec3(touchPos.x, touchPos.y, 0);

        if (!this.isPointInContainer(worldPos)) {
            return; // 触摸点不在容器内，忽略
        }

        console.log('🌐 DraggableMapContainer: 全局触摸开始，打断惯性动画');
        // 立即强制停止任何现有状态
        this.forceStopInertia();
        this.forceResetStates();

        // 立即处理，不延迟
        this.onTouchStart(event);
    }

    /**
     * 全局触摸移动
     */
    private onGlobalTouchMove(event: EventTouch): void {
        // 只有在有活动拖拽或缩放时才处理
        if (this._isDragging || this._isZooming) {
            this.onTouchMove(event);
        }
    }

    /**
     * 全局触摸结束
     */
    private onGlobalTouchEnd(event: EventTouch): void {
        // 只有在有活动拖拽或缩放时才处理
        if (this._isDragging || this._isZooming) {
            console.log('🌐 DraggableMapContainer: 全局触摸结束');
            this.onTouchEnd(event);
        }
    }

    /**
     * 检查触摸点是否在容器内
     */
    private isPointInContainer(worldPos: Vec3): boolean {
        try {
            const uiTransform = this.node.getComponent(UITransform);
            if (!uiTransform) return false;

            // 将世界坐标转换为节点本地坐标
            const localPos = new Vec3();
            uiTransform.convertToNodeSpaceAR(worldPos, localPos);

            // 检查是否在节点范围内
            const size = uiTransform.contentSize;
            const anchorPoint = uiTransform.anchorPoint;

            const minX = -size.width * anchorPoint.x;
            const maxX = size.width * (1 - anchorPoint.x);
            const minY = -size.height * anchorPoint.y;
            const maxY = size.height * (1 - anchorPoint.y);

            return localPos.x >= minX && localPos.x <= maxX &&
                   localPos.y >= minY && localPos.y <= maxY;
        } catch (error) {
            console.warn('🌐 DraggableMapContainer: 坐标转换失败', error);
            return true; // 出错时默认允许
        }
    }

    /**
     * 获取两点间距离
     */
    private getTouchDistance(touches: any[]): number {
        if (touches.length < 2) return 0;
        
        const touch1 = touches[0].getUILocation();
        const touch2 = touches[1].getUILocation();
        
        return Math.sqrt(
            Math.pow(touch2.x - touch1.x, 2) + 
            Math.pow(touch2.y - touch1.y, 2)
        );
    }

    /**
     * 应用边界限制（考虑缩放因子）
     */
    private applyBounds(position: Vec3): void {
        if (!this._config.enableBounds) {
            return; // 如果禁用边界限制，直接返回
        }

        // 获取当前缩放
        const currentScale = this.node.getScale();
        const scaleX = currentScale.x;
        const scaleY = currentScale.y;

        // 计算动态边界
        const dynamicBounds = this.calculateDynamicBounds(scaleX, scaleY);

        // 应用边界限制
        const originalX = position.x;
        const originalY = position.y;

        position.x = Math.max(dynamicBounds.left,
                    Math.min(dynamicBounds.right, position.x));
        position.y = Math.max(dynamicBounds.bottom,
                    Math.min(dynamicBounds.top, position.y));

        // 只在位置被限制时输出日志
        if (originalX !== position.x || originalY !== position.y) {
            console.log('🔒 DraggableMapContainer: 位置被边界限制', {
                original: { x: originalX, y: originalY },
                limited: { x: position.x, y: position.y },
                scale: { x: scaleX, y: scaleY },
                bounds: dynamicBounds
            });
        }
    }

    /**
     * 计算动态边界（根据缩放和内容大小）
     */
    private calculateDynamicBounds(scaleX: number, scaleY: number): any {
        // 获取容器大小
        const containerTransform = this.node.getComponent(UITransform);
        const mapContainer = this.node.getChildByName('MapContainer');

        if (!containerTransform || !mapContainer) {
            // 如果没有UITransform或地图容器，使用配置的固定边界
            return {
                left: this._config.bounds.left,
                right: this._config.bounds.right,
                top: this._config.bounds.top,
                bottom: this._config.bounds.bottom
            };
        }

        // 获取地图内容大小
        const mapTransform = mapContainer.getComponent(UITransform);
        if (!mapTransform) {
            // 如果地图容器没有UITransform，使用固定边界
            return {
                left: this._config.bounds.left,
                right: this._config.bounds.right,
                top: this._config.bounds.top,
                bottom: this._config.bounds.bottom
            };
        }

        const containerSize = containerTransform.contentSize;
        const mapSize = mapTransform.contentSize;
        const scaledMapWidth = mapSize.width * scaleX;
        const scaledMapHeight = mapSize.height * scaleY;

        // 计算可移动范围
        // 当地图小于容器时，限制在中心区域
        // 当地图大于容器时，允许移动以显示所有内容
        const maxOffsetX = Math.max(0, (scaledMapWidth - containerSize.width) / 2);
        const maxOffsetY = Math.max(0, (scaledMapHeight - containerSize.height) / 2);

        return {
            left: -maxOffsetX,
            right: maxOffsetX,
            top: maxOffsetY,
            bottom: -maxOffsetY
        };
    }

    /**
     * 开始惯性动画
     */
    private startInertia(): void {
        // 强制停止任何现有的惯性动画
        this.forceStopInertia();

        const startPosition = this.node.getPosition();
        const targetPosition = new Vec3(
            startPosition.x + this._velocity.x * 0.5,
            startPosition.y + this._velocity.y * 0.5,
            startPosition.z
        );

        // 应用边界限制
        this.applyBounds(targetPosition);

        console.log('🎮 DraggableMapContainer: 开始惯性动画');

        this._inertiaTween = tween(this.node)
            .to(1.0, { position: targetPosition }, {
                easing: 'quadOut'
            })
            .call(() => {
                // 惯性动画完成后清理引用和解除输入屏蔽
                this._inertiaTween = null;
                DraggableMapContainer.setInputBlocking(false);
                console.log('🎮 DraggableMapContainer: 惯性动画完成，解除输入屏蔽');
            })
            .start();
    }

    /**
     * 停止惯性动画
     */
    private stopInertia(): void {
        if (this._inertiaTween) {
            this._inertiaTween.stop();
            this._inertiaTween = null;
        }
    }

    /**
     * 强制停止惯性动画（用于用户打断）
     */
    private forceStopInertia(): void {
        if (this._inertiaTween) {
            this._inertiaTween.stop();
            this._inertiaTween = null;
            // 立即解除输入屏蔽
            DraggableMapContainer.setInputBlocking(false);
            console.log('🎮 DraggableMapContainer: 强制停止惯性动画，解除输入屏蔽');
        }
    }

    /**
     * 重置所有状态
     */
    private resetStates(): void {
        // 只在没有活动触摸时重置状态
        if (!this._isDragging && !this._isZooming) {
            this._velocity.set(0, 0);
            this._lastMoveTime = 0;
        }
    }

    /**
     * 强制重置所有状态（用于异常情况）
     */
    private forceResetStates(): void {
        console.warn('🎮 DraggableMapContainer: 强制重置状态');
        this._isDragging = false;
        this._isZooming = false;
        this._velocity.set(0, 0);
        this._lastMoveTime = 0;
        this.stopInertia();
    }

    // ==================== 公共API ====================

    /**
     * 设置拖拽配置
     */
    public setConfig(config: Partial<IDragConfig>): void {
        this._config = { ...this._config, ...config };
        this.updateConfig();
    }

    /**
     * 设置边界
     */
    public setBounds(left: number, right: number, top: number, bottom: number): void {
        this._config.bounds = { left, right, top, bottom };
        console.log('🔒 DraggableMapContainer: 边界已更新', this._config.bounds);
    }

    /**
     * 启用/禁用边界限制
     */
    public setBoundsEnabled(enabled: boolean): void {
        this._config.enableBounds = enabled;
        console.log('🔒 DraggableMapContainer: 边界限制', enabled ? '启用' : '禁用');
    }

    /**
     * 获取当前边界设置
     */
    public getBounds(): any {
        const currentScale = this.node.getScale();
        return {
            enabled: this._config.enableBounds,
            originalBounds: this._config.bounds,
            dynamicBounds: this.calculateDynamicBounds(currentScale.x, currentScale.y),
            currentScale: { x: currentScale.x, y: currentScale.y }
        };
    }

    /**
     * 移动到指定位置
     */
    public moveTo(position: Vec3, duration: number = 0.5): void {
        this.stopInertia();
        this.forceResetStates();

        const targetPosition = new Vec3(position);
        this.applyBounds(targetPosition);

        if (duration > 0) {
            tween(this.node)
                .to(duration, { position: targetPosition }, {
                    easing: 'quadInOut'
                })
                .start();
        } else {
            this.node.setPosition(targetPosition);
        }
    }

    /**
     * 手动重置拖拽状态（公共API）
     */
    public resetDragState(): void {
        console.log('🎮 DraggableMapContainer: 手动重置拖拽状态');
        this.forceStopInertia();
        this.forceResetStates();
        // 确保解除输入屏蔽
        DraggableMapContainer.setInputBlocking(false);
    }

    /**
     * 手动打断惯性动画（公共API）
     */
    public interruptInertia(): void {
        console.log('🎮 DraggableMapContainer: 手动打断惯性动画');
        this.forceStopInertia();
    }

    /**
     * 检查是否正在进行惯性动画
     */
    public isInertiaActive(): boolean {
        return this._inertiaTween !== null;
    }

    // ==================== 静态输入屏蔽管理 ====================

    /**
     * 设置输入屏蔽状态
     */
    public static setInputBlocking(blocking: boolean): void {
        DraggableMapContainer._isBlockingInput = blocking;
        console.log('🚫 DraggableMapContainer: 输入屏蔽', blocking ? '开启' : '关闭');
    }

    /**
     * 检查是否正在屏蔽输入
     */
    public static isInputBlocked(): boolean {
        return DraggableMapContainer._isBlockingInput;
    }

    /**
     * 强制解除输入屏蔽
     */
    public static clearInputBlocking(): void {
        DraggableMapContainer._isBlockingInput = false;
        console.log('🚫 DraggableMapContainer: 强制解除输入屏蔽');
    }

    /**
     * 开始状态检查定时器
     */
    private startStateCheck(): void {
        this._stateCheckTimer = setInterval(() => {
            this.checkAndCleanStates();
        }, 2000); // 每2秒检查一次
    }

    /**
     * 停止状态检查定时器
     */
    private stopStateCheck(): void {
        if (this._stateCheckTimer) {
            clearInterval(this._stateCheckTimer);
            this._stateCheckTimer = 0;
        }
    }

    /**
     * 检查并清理异常状态
     */
    private checkAndCleanStates(): void {
        const now = Date.now();

        // 如果拖拽状态持续时间过长（超过5秒），强制重置
        if (this._isDragging && this._lastMoveTime > 0 && (now - this._lastMoveTime) > 5000) {
            console.warn('🎮 DraggableMapContainer: 检测到拖拽状态异常，自动重置');
            this.forceResetStates();
        }

        // 如果缩放状态持续时间过长，强制重置
        if (this._isZooming && this._lastMoveTime > 0 && (now - this._lastMoveTime) > 5000) {
            console.warn('🎮 DraggableMapContainer: 检测到缩放状态异常，自动重置');
            this.forceResetStates();
        }
    }

    /**
     * 缩放到指定比例
     */
    public scaleTo(scale: number, duration: number = 0.3): void {
        scale = Math.max(this._config.minScale, Math.min(this._config.maxScale, scale));
        
        if (duration > 0) {
            tween(this.node)
                .to(duration, { scale: new Vec3(scale, scale, 1) }, {
                    easing: 'quadInOut'
                })
                .start();
        } else {
            this.node.setScale(scale, scale, 1);
        }
    }
}
