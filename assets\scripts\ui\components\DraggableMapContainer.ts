/**
 * 可拖拽地图容器组件
 * 提供地图的拖拽、缩放和边界限制功能
 */

import { _decorator, Component, Node, Vec3, input, Input, EventTouch, UITransform, tween, Tween } from 'cc';

const { ccclass, property } = _decorator;

/**
 * 拖拽配置接口
 */
export interface IDragConfig {
    /** 是否启用拖拽 */
    enableDrag: boolean;
    
    /** 是否启用缩放 */
    enableZoom: boolean;
    
    /** 拖拽阻尼系数 */
    dragDamping: number;
    
    /** 缩放敏感度 */
    zoomSensitivity: number;
    
    /** 最小缩放 */
    minScale: number;
    
    /** 最大缩放 */
    maxScale: number;
    
    /** 拖拽边界 */
    bounds: {
        left: number;
        right: number;
        top: number;
        bottom: number;
    };
    
    /** 是否启用惯性 */
    enableInertia: boolean;
    
    /** 惯性阻尼 */
    inertiaDamping: number;
}

@ccclass('DraggableMapContainer')
export class DraggableMapContainer extends Component {
    
    @property({ tooltip: '是否启用拖拽' })
    public enableDrag: boolean = true;
    
    @property({ tooltip: '是否启用缩放' })
    public enableZoom: boolean = true;
    
    @property({ tooltip: '拖拽阻尼系数', range: [0.1, 1.0] })
    public dragDamping: number = 0.8;
    
    @property({ tooltip: '缩放敏感度', range: [0.01, 1.0] })
    public zoomSensitivity: number = 0.1;
    
    @property({ tooltip: '最小缩放', range: [0.1, 1.0] })
    public minScale: number = 0.5;
    
    @property({ tooltip: '最大缩放', range: [1.0, 5.0] })
    public maxScale: number = 2.0;
    
    @property({ tooltip: '是否启用惯性' })
    public enableInertia: boolean = true;
    
    @property({ tooltip: '惯性阻尼', range: [0.1, 1.0] })
    public inertiaDamping: number = 0.95;
    
    // 私有属性
    private _config: IDragConfig = {
        enableDrag: true,
        enableZoom: true,
        dragDamping: 0.8,
        zoomSensitivity: 0.1,
        minScale: 0.5,
        maxScale: 2.0,
        bounds: {
            left: -500,
            right: 500,
            top: 300,
            bottom: -300
        },
        enableInertia: true,
        inertiaDamping: 0.95
    };
    
    // 拖拽状态
    private _isDragging: boolean = false;
    private _lastTouchPosition: Vec3 = new Vec3();
    private _dragStartPosition: Vec3 = new Vec3();
    private _velocity: Vec3 = new Vec3();
    private _lastMoveTime: number = 0;
    
    // 缩放状态
    private _isZooming: boolean = false;
    private _lastTouchDistance: number = 0;
    
    // 惯性动画
    private _inertiaTween: Tween<Node> | null = null;

    protected onLoad(): void {
        console.log('🎮 DraggableMapContainer: 可拖拽地图容器加载');
        this.updateConfig();
        this.setupInputEvents();
    }

    protected onDestroy(): void {
        this.cleanupInputEvents();
        this.stopInertia();
    }

    /**
     * 更新配置
     */
    private updateConfig(): void {
        this._config.enableDrag = this.enableDrag;
        this._config.enableZoom = this.enableZoom;
        this._config.dragDamping = this.dragDamping;
        this._config.zoomSensitivity = this.zoomSensitivity;
        this._config.minScale = this.minScale;
        this._config.maxScale = this.maxScale;
        this._config.enableInertia = this.enableInertia;
        this._config.inertiaDamping = this.inertiaDamping;
    }

    /**
     * 设置输入事件
     */
    private setupInputEvents(): void {
        this.node.on(Input.EventType.TOUCH_START, this.onTouchStart, this);
        this.node.on(Input.EventType.TOUCH_MOVE, this.onTouchMove, this);
        this.node.on(Input.EventType.TOUCH_END, this.onTouchEnd, this);
        this.node.on(Input.EventType.TOUCH_CANCEL, this.onTouchEnd, this);
        
        console.log('🎮 DraggableMapContainer: 输入事件设置完成');
    }

    /**
     * 清理输入事件
     */
    private cleanupInputEvents(): void {
        this.node.off(Input.EventType.TOUCH_START, this.onTouchStart, this);
        this.node.off(Input.EventType.TOUCH_MOVE, this.onTouchMove, this);
        this.node.off(Input.EventType.TOUCH_END, this.onTouchEnd, this);
        this.node.off(Input.EventType.TOUCH_CANCEL, this.onTouchEnd, this);
    }

    /**
     * 触摸开始
     */
    private onTouchStart(event: EventTouch): void {
        if (!this._config.enableDrag) return;
        
        const touches = event.getAllTouches();
        
        if (touches.length === 1) {
            // 单点触摸 - 拖拽
            this.startDrag(event);
        } else if (touches.length === 2 && this._config.enableZoom) {
            // 双点触摸 - 缩放
            this.startZoom(touches);
        }
        
        // 停止惯性动画
        this.stopInertia();
    }

    /**
     * 触摸移动
     */
    private onTouchMove(event: EventTouch): void {
        const touches = event.getAllTouches();
        
        if (touches.length === 1 && this._isDragging) {
            // 单点拖拽
            this.updateDrag(event);
        } else if (touches.length === 2 && this._isZooming) {
            // 双点缩放
            this.updateZoom(touches);
        }
    }

    /**
     * 触摸结束
     */
    private onTouchEnd(event: EventTouch): void {
        const touches = event.getAllTouches();
        
        if (touches.length === 0) {
            // 所有触摸结束
            if (this._isDragging) {
                this.endDrag();
            }
            if (this._isZooming) {
                this.endZoom();
            }
        } else if (touches.length === 1) {
            // 从双点变为单点
            if (this._isZooming) {
                this.endZoom();
                this.startDrag(event);
            }
        }
    }

    /**
     * 开始拖拽
     */
    private startDrag(event: EventTouch): void {
        this._isDragging = true;
        this._lastTouchPosition = event.getUILocation();
        this._dragStartPosition.set(this._lastTouchPosition);
        this._velocity.set(0, 0, 0);
        this._lastMoveTime = Date.now();
        
        console.log('🎮 DraggableMapContainer: 开始拖拽');
    }

    /**
     * 更新拖拽
     */
    private updateDrag(event: EventTouch): void {
        const currentPosition = event.getUILocation();
        const deltaPosition = new Vec3(
            currentPosition.x - this._lastTouchPosition.x,
            currentPosition.y - this._lastTouchPosition.y,
            0
        );
        
        // 应用阻尼
        deltaPosition.multiplyScalar(this._config.dragDamping);
        
        // 更新位置
        const currentNodePosition = this.node.getPosition();
        const newPosition = new Vec3(
            currentNodePosition.x + deltaPosition.x,
            currentNodePosition.y + deltaPosition.y,
            currentNodePosition.z
        );
        
        // 应用边界限制
        this.applyBounds(newPosition);
        this.node.setPosition(newPosition);
        
        // 计算速度（用于惯性）
        const currentTime = Date.now();
        const deltaTime = (currentTime - this._lastMoveTime) / 1000;
        if (deltaTime > 0) {
            this._velocity.set(deltaPosition.x / deltaTime, deltaPosition.y / deltaTime, 0);
        }
        
        this._lastTouchPosition = currentPosition;
        this._lastMoveTime = currentTime;
    }

    /**
     * 结束拖拽
     */
    private endDrag(): void {
        this._isDragging = false;
        
        // 启动惯性动画
        if (this._config.enableInertia && this._velocity.length() > 50) {
            this.startInertia();
        }
        
        console.log('🎮 DraggableMapContainer: 结束拖拽');
    }

    /**
     * 开始缩放
     */
    private startZoom(touches: any[]): void {
        this._isZooming = true;
        this._lastTouchDistance = this.getTouchDistance(touches);
        
        console.log('🎮 DraggableMapContainer: 开始缩放');
    }

    /**
     * 更新缩放
     */
    private updateZoom(touches: any[]): void {
        const currentDistance = this.getTouchDistance(touches);
        const deltaDistance = currentDistance - this._lastTouchDistance;
        
        if (Math.abs(deltaDistance) > 5) { // 防止微小变化
            const scaleChange = deltaDistance * this._config.zoomSensitivity * 0.01;
            const currentScale = this.node.getScale().x;
            const newScale = Math.max(this._config.minScale, 
                            Math.min(this._config.maxScale, currentScale + scaleChange));
            
            this.node.setScale(newScale, newScale, 1);
            this._lastTouchDistance = currentDistance;
        }
    }

    /**
     * 结束缩放
     */
    private endZoom(): void {
        this._isZooming = false;
        console.log('🎮 DraggableMapContainer: 结束缩放');
    }

    /**
     * 获取两点间距离
     */
    private getTouchDistance(touches: any[]): number {
        if (touches.length < 2) return 0;
        
        const touch1 = touches[0].getUILocation();
        const touch2 = touches[1].getUILocation();
        
        return Math.sqrt(
            Math.pow(touch2.x - touch1.x, 2) + 
            Math.pow(touch2.y - touch1.y, 2)
        );
    }

    /**
     * 应用边界限制
     */
    private applyBounds(position: Vec3): void {
        position.x = Math.max(this._config.bounds.left, 
                    Math.min(this._config.bounds.right, position.x));
        position.y = Math.max(this._config.bounds.bottom, 
                    Math.min(this._config.bounds.top, position.y));
    }

    /**
     * 开始惯性动画
     */
    private startInertia(): void {
        this.stopInertia();
        
        const startPosition = this.node.getPosition();
        const targetPosition = new Vec3(
            startPosition.x + this._velocity.x * 0.5,
            startPosition.y + this._velocity.y * 0.5,
            startPosition.z
        );
        
        // 应用边界限制
        this.applyBounds(targetPosition);
        
        this._inertiaTween = tween(this.node)
            .to(1.0, { position: targetPosition }, {
                easing: 'quadOut'
            })
            .start();
    }

    /**
     * 停止惯性动画
     */
    private stopInertia(): void {
        if (this._inertiaTween) {
            this._inertiaTween.stop();
            this._inertiaTween = null;
        }
    }

    // ==================== 公共API ====================

    /**
     * 设置拖拽配置
     */
    public setConfig(config: Partial<IDragConfig>): void {
        this._config = { ...this._config, ...config };
        this.updateConfig();
    }

    /**
     * 设置边界
     */
    public setBounds(left: number, right: number, top: number, bottom: number): void {
        this._config.bounds = { left, right, top, bottom };
    }

    /**
     * 移动到指定位置
     */
    public moveTo(position: Vec3, duration: number = 0.5): void {
        this.stopInertia();
        
        const targetPosition = new Vec3(position);
        this.applyBounds(targetPosition);
        
        if (duration > 0) {
            tween(this.node)
                .to(duration, { position: targetPosition }, {
                    easing: 'quadInOut'
                })
                .start();
        } else {
            this.node.setPosition(targetPosition);
        }
    }

    /**
     * 缩放到指定比例
     */
    public scaleTo(scale: number, duration: number = 0.3): void {
        scale = Math.max(this._config.minScale, Math.min(this._config.maxScale, scale));
        
        if (duration > 0) {
            tween(this.node)
                .to(duration, { scale: new Vec3(scale, scale, 1) }, {
                    easing: 'quadInOut'
                })
                .start();
        } else {
            this.node.setScale(scale, scale, 1);
        }
    }
}
